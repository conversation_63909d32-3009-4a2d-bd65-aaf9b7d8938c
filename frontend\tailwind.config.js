/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#fef9f3',
          100: '#fef2e6',
          200: '#fde2cc',
          300: '#fbcca8',
          400: '#f8a973',
          500: '#f4803c',
          600: '#e85d1c',
          700: '#c14917',
          800: '#9a3b18',
          900: '#7c3117',
        },
        secondary: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
        },
        accent: {
          50: '#fef7f0',
          100: '#fdeee0',
          200: '#fad9c1',
          300: '#f6be97',
          400: '#f19a6b',
          500: '#ed7c47',
          600: '#e85d1c',
          700: '#c14917',
          800: '#9a3b18',
          900: '#7c3117',
        },
        heritage: {
          50: '#fefdf9',
          100: '#fefbf3',
          200: '#fcf4e6',
          300: '#f9e8d0',
          400: '#f4d5a7',
          500: '#edbf7a',
          600: '#e4a853',
          700: '#d18f2e',
          800: '#b8751a',
          900: '#9a5f15',
        },
        temple: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        },
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        error: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        // Authentic Tamil Cultural Colors
        manuscript: {
          50: '#fffaf0',
          100: '#fef5e7',
          200: '#fdebd0',
          300: '#fce1b8',
          400: '#fbd7a0',
          500: '#f9cd88',
          600: '#d7b177',
          700: '#b59566',
          800: '#937955',
          900: '#715d44',
        },
        saffron: {
          50: '#fff7ed',
          100: '#ffedd5',
          200: '#fed7aa',
          300: '#fdba74',
          400: '#fb923c',
          500: '#f97316',
          600: '#ea580c',
          700: '#c2410c',
          800: '#9a3412',
          900: '#7c2d12',
        },
        royal: {
          50: '#faf5ff',
          100: '#f3e8ff',
          200: '#e9d5ff',
          300: '#d8b4fe',
          400: '#c084fc',
          500: '#a855f7',
          600: '#9333ea',
          700: '#7c3aed',
          800: '#6b21a8',
          900: '#581c87',
        },
        palmleaf: {
          50: '#fdf6e3',
          100: '#f9f0d3',
          200: '#f3e1a7',
          300: '#ecd07b',
          400: '#e4bf4f',
          500: '#d4a373',
          600: '#b8956a',
          700: '#9c8761',
          800: '#807958',
          900: '#646b4f',
        }
      },
      fontFamily: {
        'tamil': ['Anek Tamil', 'Noto Sans Tamil', 'sans-serif'],
        'english': ['Inter', 'sans-serif'],
        'anek-tamil': ['Anek Tamil', 'sans-serif'],
        'noto-tamil': ['Noto Sans Tamil', 'sans-serif'],
        'manuscript': ['Playfair Display', 'Cormorant Garamond', 'serif'],
        'traditional': ['Catamaran', 'Anek Tamil', 'serif'],
      },
      animation: {
        "fade-in": "fadeIn 0.5s ease-in-out",
        "slide-up": "slideUp 0.3s ease-out",
        "scale-in": "scaleIn 0.2s ease-out",
        "pulse-slow": "pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite",
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        slideUp: {
          "0%": { transform: "translateY(10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
        scaleIn: {
          "0%": { transform: "scale(0.95)", opacity: "0" },
          "100%": { transform: "scale(1)", opacity: "1" },
        },
      },
      boxShadow: {
        'glow': '0 0 20px rgba(237, 118, 17, 0.3)',
        'glow-lg': '0 0 40px rgba(237, 118, 17, 0.4)',
      }
    },
  },
  plugins: [],
}
