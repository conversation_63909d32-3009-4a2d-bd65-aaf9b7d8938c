import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link, useSearchParams } from 'react-router-dom';
import { useTheme } from '../hooks/useTheme';
import { kuralAPI } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';
import {
  BookOpen,
  Heart,
  Eye,
  ChevronLeft,
  ChevronRight,
  ArrowLeft
} from 'lucide-react';

const CategoryView = () => {
  const { paal, adhigaram } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const { isTamil } = useTheme();

  const [kurals, setKurals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({});

  const currentPage = parseInt(searchParams.get('page')) || 1;
  const limit = 12;

  // Decode URL parameters
  const decodedPaal = paal ? decodeURIComponent(paal) : null;
  const decodedAdhigaram = adhigaram ? decodeURIComponent(adhigaram) : null;

  const categoryInfo = {
    'அறம்': {
      nameEn: 'Virtue',
      description: isTamil
        ? 'நல்லொழுக்கம், அறநெறி, ஒழுக்கம் பற்றிய குறள்கள்'
        : 'Kurals about ethics, righteousness, and moral conduct',
      color: 'from-green-500 to-emerald-600',
      icon: '🕊️',
      totalKurals: 380
    },
    'பொருள்': {
      nameEn: 'Wealth',
      description: isTamil
        ? 'செல்வம், அரசியல், பொருளாதாரம் பற்றிய குறள்கள்'
        : 'Kurals about prosperity, governance, and economics',
      color: 'from-yellow-500 to-orange-600',
      icon: '👑',
      totalKurals: 700
    },
    'இன்பம்': {
      nameEn: 'Love',
      description: isTamil
        ? 'காதல், இன்பம், உறவுகள் பற்றிய குறள்கள்'
        : 'Kurals about love, pleasure, and relationships',
      color: 'from-pink-500 to-rose-600',
      icon: '💝',
      totalKurals: 250
    }
  };

  const currentCategory = categoryInfo[decodedPaal];
  const isAdhigaramView = !!decodedAdhigaram;

  useEffect(() => {
    fetchKurals();
  }, [decodedPaal, decodedAdhigaram, currentPage]);

  const fetchKurals = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        page: currentPage,
        limit
      };

      let response;
      if (isAdhigaramView) {
        response = await kuralAPI.getKuralsByAdhigaram(decodedAdhigaram, params);
      } else {
        response = await kuralAPI.getKuralsByPaal(decodedPaal, params);
      }

      setKurals(response.data.data);
      setPagination(response.data.pagination);
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to fetch kurals');
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (newPage) => {
    const params = new URLSearchParams(searchParams);
    params.set('page', newPage.toString());
    setSearchParams(params);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" text={isTamil ? 'குறள்கள் ஏற்றுகிறது...' : 'Loading kurals...'} />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">{error}</div>
          <button
            onClick={fetchKurals}
            className="btn-primary btn-md"
          >
            {isTamil ? 'மீண்டும் முயற்சி' : 'Try Again'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary-100/10 to-secondary-100/10"></div>
      <div className="absolute top-10 right-10 w-40 h-40 bg-primary-200 rounded-full opacity-5 animate-pulse-slow"></div>
      <div className="absolute bottom-10 left-10 w-32 h-32 bg-secondary-200 rounded-full opacity-5 animate-pulse-slow" style={{animationDelay: '1.5s'}}></div>

      <div className="container mx-auto px-4 py-8 relative z-10">
        {/* Navigation */}
        <div className="mb-6 animate-fade-in">
          <Link
            to={isAdhigaramView ? `/category/${encodeURIComponent(decodedPaal)}` : '/kurals'}
            className="btn-ghost btn-md hover:bg-primary-100 transition-all duration-300"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            {isTamil ? 'பின்செல்' : 'Back'}
          </Link>
        </div>

        {/* Header */}
        <div className="text-center mb-8 animate-slide-up">
          {currentCategory && !isAdhigaramView && (
            <div className={`w-20 h-20 mx-auto mb-4 rounded-full bg-gradient-to-r ${currentCategory.color} flex items-center justify-center text-4xl shadow-lg hover:shadow-xl transition-all duration-300 animate-bounce`}>
              {currentCategory.icon}
            </div>
          )}

          <h1 className="text-4xl font-bold gradient-text mb-4">
            {isAdhigaramView ? (
              <>
                {decodedAdhigaram}
                <div className="text-lg text-muted-foreground mt-2">
                  {decodedPaal} {isTamil ? 'பாலின் அதிகாரம்' : 'Chapter'}
                </div>
              </>
            ) : (
              <>
                {isTamil ? decodedPaal : currentCategory?.nameEn}
                <div className="text-lg text-muted-foreground mt-2">
                  {isTamil ? 'பால்' : 'Section'}
                </div>
              </>
            )}
          </h1>

          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            {isAdhigaramView ? (
              isTamil ?
                `${decodedAdhigaram} அதிகாரத்தின் குறள்கள்` :
                `Kurals from ${decodedAdhigaram} chapter`
            ) : (
              currentCategory?.description
            )}
          </p>

          <div className="mt-4 text-sm text-muted-foreground">
            {pagination.totalItems ? (
              isTamil ?
                `மொத்தம் ${pagination.totalItems} குறள்கள்` :
                `${pagination.totalItems} total kurals`
            ) : (
              isTamil ? 'குறள்கள் ஏற்றுகிறது...' : 'Loading kurals...'
            )}
          </div>
        </div>

        {kurals.length === 0 ? (
          <div className="text-center py-16">
            <BookOpen className="w-24 h-24 text-gray-300 mx-auto mb-6" />
            <h2 className="text-2xl font-semibold mb-4">
              {isTamil ? 'குறள்கள் கிடைக்கவில்லை' : 'No Kurals Found'}
            </h2>
            <p className="text-muted-foreground">
              {isTamil
                ? 'இந்த பிரிவில் குறள்கள் கிடைக்கவில்லை.'
                : 'No kurals found in this section.'
              }
            </p>
          </div>
        ) : (
          <>
            {/* Kurals Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {kurals.map((kural) => (
                <CategoryKuralCard key={kural._id} kural={kural} isTamil={isTamil} />
              ))}
            </div>

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="flex justify-center items-center space-x-4">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={!pagination.hasPrevPage}
                  className="btn-outline btn-md disabled:opacity-50"
                >
                  <ChevronLeft className="w-4 h-4 mr-2" />
                  {isTamil ? 'முந்தைய' : 'Previous'}
                </button>

                <div className="flex items-center space-x-2">
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    const pageNum = Math.max(1, currentPage - 2) + i;
                    if (pageNum > pagination.totalPages) return null;

                    return (
                      <button
                        key={pageNum}
                        onClick={() => handlePageChange(pageNum)}
                        className={`w-10 h-10 rounded-md ${
                          pageNum === currentPage
                            ? 'bg-primary-600 text-white'
                            : 'bg-white border hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={!pagination.hasNextPage}
                  className="btn-outline btn-md disabled:opacity-50"
                >
                  {isTamil ? 'அடுத்த' : 'Next'}
                  <ChevronRight className="w-4 h-4 ml-2" />
                </button>
              </div>
            )}

            {/* Results Info */}
            <div className="text-center text-muted-foreground mt-4">
              {isTamil ? (
                <>
                  பக்கம் {pagination.currentPage} / {pagination.totalPages}
                  (மொத்தம் {pagination.totalItems} குறள்கள்)
                </>
              ) : (
                <>
                  Page {pagination.currentPage} of {pagination.totalPages}
                  ({pagination.totalItems} total kurals)
                </>
              )}
            </div>
          </>
        )}

        {/* Related Sections */}
        {!isAdhigaramView && currentCategory && (
          <div className="mt-16">
            <h2 className="text-2xl font-bold text-center mb-8">
              {isTamil ? 'மற்ற பால்கள்' : 'Other Sections'}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
              {Object.entries(categoryInfo)
                .filter(([key]) => key !== decodedPaal)
                .map(([key, info]) => (
                  <Link
                    key={key}
                    to={`/category/${encodeURIComponent(key)}`}
                    className="group"
                  >
                    <div className="card hover:shadow-lg transition-all duration-300 group-hover:scale-105">
                      <div className="card-content text-center">
                        <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r ${info.color} flex items-center justify-center text-2xl`}>
                          {info.icon}
                        </div>
                        <h3 className="text-xl font-bold mb-2">
                          {isTamil ? key : info.nameEn}
                        </h3>
                        <p className="text-muted-foreground text-sm">
                          {info.description}
                        </p>
                        <div className="mt-3 text-xs text-primary-600">
                          {info.totalKurals} {isTamil ? 'குறள்கள்' : 'kurals'}
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Category Kural Card Component
const CategoryKuralCard = ({ kural, isTamil }) => {
  return (
    <Link to={`/kural/${kural.number}`} className="group">
      <div className="card hover:shadow-lg transition-all duration-300 group-hover:scale-105 h-full">
        <div className="card-content">
          {/* Header */}
          <div className="flex justify-between items-start mb-4">
            <div>
              <div className="text-sm text-primary-600 font-medium">
                {isTamil ? 'குறள்' : 'Kural'} {kural.number}
              </div>
              <div className="text-xs text-muted-foreground">
                {kural.adhigaram}
              </div>
            </div>
            <div className="text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded">
              {kural.paal}
            </div>
          </div>

          {/* Kural Text */}
          <div className="kural-text text-center mb-4 text-primary-800">
            {kural.line1}<br />
            {kural.line2}
          </div>

          {/* Meaning */}
          <p className="kural-meaning text-sm line-clamp-3">
            {isTamil ? kural.meaning_ta : kural.meaning_en}
          </p>

          {/* Footer */}
          <div className="flex justify-between items-center mt-4 pt-4 border-t">
            <div className="flex items-center space-x-4 text-xs text-muted-foreground">
              <div className="flex items-center">
                <Eye className="w-3 h-3 mr-1" />
                {kural.metadata?.views || 0}
              </div>
              <div className="flex items-center">
                <Heart className="w-3 h-3 mr-1" />
                {kural.metadata?.favorites_count || 0}
              </div>
            </div>
            <BookOpen className="w-4 h-4 text-primary-600 group-hover:text-primary-700" />
          </div>
        </div>
      </div>
    </Link>
  );
};

export default CategoryView;
