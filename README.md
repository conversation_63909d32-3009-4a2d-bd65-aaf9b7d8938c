# KuralVerse 🧠

A culturally rich, full-stack MERN application presenting the Thirukkural with detailed commentaries, categorized views, and an interactive chatbot styled as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.

## 🌟 Features

- **Complete Kural Collection**: All 1330 Kurals with Tamil and English meanings
- **Multiple Commentaries**: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>gi commentaries
- **Category Navigation**: Browse by <PERSON><PERSON> (அறம், பொருள், இன்பம்) and <PERSON><PERSON><PERSON><PERSON>
- **Smart Search**: Search by Kural number or content
- **Kural of the Day**: Daily featured Kural
- **AI Chatbot**: Interactive chat with <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> using Gemini AI
- **Multilingual Support**: Tamil and English interface
- **Responsive Design**: Works on all devices
- **User Features**: Comments and favorites (with authentication)

## 🛠️ Tech Stack

### Frontend
- **React 18** with Vite
- **Tailwind CSS** for styling
- **React Router** for navigation
- **Axios** for API calls
- **Lucide React** for icons

### Backend
- **Node.js** with Express
- **MongoDB** with Mongoose
- **JWT** authentication
- **Gemini AI** integration
- **CORS** and security middleware

### Database
- **MongoDB Atlas** (Free tier)
- Indexed search fields
- Structured Kural documents

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- MongoDB Atlas account
- Gemini AI API key

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd KuralVerse
```

2. **Backend Setup**
```bash
cd backend
npm install
cp .env.example .env
# Edit .env with your MongoDB and Gemini API credentials
npm run dev
```

3. **Frontend Setup**
```bash
cd frontend
npm install
npm run dev
```

4. **Environment Variables**

Backend (.env):
```
MONGODB_URI=your_mongodb_connection_string
JWT_SECRET=your_jwt_secret
GEMINI_API_KEY=your_gemini_api_key
PORT=5000
```

## 📁 Project Structure

```
KuralVerse/
├── frontend/                 # React application
│   ├── src/
│   │   ├── components/      # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom hooks
│   │   ├── services/       # API services
│   │   └── utils/          # Utility functions
│   └── public/
├── backend/                 # Express API
│   ├── models/             # MongoDB models
│   ├── routes/             # API routes
│   ├── middleware/         # Custom middleware
│   ├── controllers/        # Route controllers
│   └── utils/              # Utility functions
└── README.md
```

## 🔗 API Endpoints

### Kurals
- `GET /api/kurals` - Get all kurals (with pagination)
- `GET /api/kurals/:number` - Get specific kural
- `GET /api/kurals/search/:query` - Search kurals
- `GET /api/kurals/paal/:paal` - Get kurals by category
- `GET /api/kurals/adhigaram/:adhigaram` - Get kurals by sub-category
- `GET /api/kurals/random` - Get random kural (Kural of the Day)

### AI Chat
- `POST /api/chat/gemini` - Chat with Thiruvalluvar AI

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile

### User Features
- `POST /api/users/favorites` - Add to favorites
- `GET /api/users/favorites` - Get user favorites
- `POST /api/users/comments` - Add comment
- `GET /api/kurals/:number/comments` - Get kural comments

## 🎯 Usage

1. **Browse Kurals**: Navigate through categories or search by number
2. **Read Commentaries**: View multiple scholarly interpretations
3. **Chat with AI**: Ask questions to the Thiruvalluvar chatbot
4. **Save Favorites**: Create an account to save favorite kurals
5. **Daily Learning**: Check the Kural of the Day feature

## 🔐 Authentication

The app uses a "lazy authentication" approach:
- **No login required**: Browse kurals, use AI chat, read content
- **Login required**: Save favorites, post comments, personalized features

## 🌍 Internationalization

- Tamil interface with proper Unicode support
- English translations and interface
- Romanized transliterations available

## 📱 Responsive Design

- Mobile-first approach
- Tablet and desktop optimized
- Touch-friendly navigation
- Accessible design principles

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the ISC License.

## 🙏 Acknowledgments

- Thiruvalluvar for the timeless wisdom
- Tamil literary scholars for commentaries
- Google Gemini AI for conversational features
- Open source community for tools and libraries
