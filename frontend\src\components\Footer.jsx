import React from 'react';
import { Link } from 'react-router-dom';
import { useTheme } from '../hooks/useTheme';
import { Heart, Github, Twitter, Mail } from 'lucide-react';

const Footer = () => {
  const { isTamil } = useTheme();

  const footerLinks = {
    explore: {
      title: isTamil ? 'ஆராய்' : 'Explore',
      links: [
        { path: '/kurals', label: isTamil ? 'அனைத்து குறள்கள்' : 'All Kurals' },
        { path: '/category/அறம்', label: isTamil ? 'அறம்' : 'Virtue' },
        { path: '/category/பொருள்', label: isTamil ? 'பொருள்' : 'Wealth' },
        { path: '/category/இன்பம்', label: isTamil ? 'இன்பம்' : 'Love' },
        { path: '/chat', label: isTamil ? 'AI உரையாடல்' : 'AI Chat' },
      ]
    },
    learn: {
      title: isTamil ? 'கற்றுக்கொள்' : 'Learn',
      links: [
        { path: '/about', label: isTamil ? 'திருக்குறள் பற்றி' : 'About Thirukkural' },
        { path: '/about#thiruvalluvar', label: isTamil ? 'திருவள்ளுவர்' : 'Thiruvalluvar' },
        { path: '/about#commentaries', label: isTamil ? 'உரைகள்' : 'Commentaries' },
        { path: '/about#structure', label: isTamil ? 'அமைப்பு' : 'Structure' },
      ]
    },
    community: {
      title: isTamil ? 'சமூகம்' : 'Community',
      links: [
        { path: '/register', label: isTamil ? 'சேர்' : 'Join Us' },
        { path: '/favorites', label: isTamil ? 'விருப்பங்கள்' : 'Favorites' },
        { path: '/profile', label: isTamil ? 'சுயவிவரம்' : 'Profile' },
      ]
    }
  };

  return (
    <footer className="bg-muted/30 border-t">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="space-y-4">
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">குறள்</span>
              </div>
              <span className="font-bold text-xl gradient-text">
                {isTamil ? 'குறள்வெர்ஸ்' : 'KuralVerse'}
              </span>
            </Link>
            <p className="text-sm text-muted-foreground max-w-xs">
              {isTamil 
                ? 'திருக்குறளின் அழியாத ஞானத்தை நவீன தொழில்நுட்பத்துடன் இணைத்து வழங்கும் தளம்.'
                : 'Bringing the timeless wisdom of Thirukkural to the modern world through technology.'
              }
            </p>
            <div className="flex space-x-4">
              <a
                href="https://github.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-primary-600 transition-colors"
                aria-label="GitHub"
              >
                <Github className="w-5 h-5" />
              </a>
              <a
                href="https://twitter.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-primary-600 transition-colors"
                aria-label="Twitter"
              >
                <Twitter className="w-5 h-5" />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="text-muted-foreground hover:text-primary-600 transition-colors"
                aria-label="Email"
              >
                <Mail className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Footer Links */}
          {Object.entries(footerLinks).map(([key, section]) => (
            <div key={key} className="space-y-4">
              <h3 className="font-semibold text-foreground">{section.title}</h3>
              <ul className="space-y-2">
                {section.links.map((link) => (
                  <li key={link.path}>
                    <Link
                      to={link.path}
                      className="text-sm text-muted-foreground hover:text-primary-600 transition-colors"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom Section */}
        <div className="mt-12 pt-8 border-t border-border">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm text-muted-foreground">
              {isTamil ? (
                <>
                  © 2024 குறள்வெர்ஸ். அனைத்து உரிமைகளும் பாதுகாக்கப்பட்டவை.
                </>
              ) : (
                <>
                  © 2024 KuralVerse. All rights reserved.
                </>
              )}
            </div>
            
            <div className="flex items-center space-x-1 text-sm text-muted-foreground">
              <span>
                {isTamil ? 'அன்புடன் உருவாக்கப்பட்டது' : 'Made with'}
              </span>
              <Heart className="w-4 h-4 text-red-500 fill-current" />
              <span>
                {isTamil ? 'தமிழ் இலக்கியத்திற்காக' : 'for Tamil literature'}
              </span>
            </div>
          </div>

          {/* Additional Info */}
          <div className="mt-6 text-xs text-muted-foreground text-center">
            <p>
              {isTamil ? (
                <>
                  திருக்குறள் - திருவள்ளுவர் அருளிய அழியாத ஞான நூல். 
                  இந்த தளம் கல்வி நோக்கத்திற்காக மட்டுமே உருவாக்கப்பட்டுள்ளது.
                </>
              ) : (
                <>
                  Thirukkural - The eternal wisdom literature by Thiruvalluvar. 
                  This platform is created for educational purposes only.
                </>
              )}
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
