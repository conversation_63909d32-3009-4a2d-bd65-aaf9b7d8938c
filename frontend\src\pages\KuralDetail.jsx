import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { useTheme } from '../hooks/useTheme';
import { kuralAPI, userAPI, chatAPI } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';
import {
  ArrowLeft,
  ArrowRight,
  Heart,
  MessageCircle,
  Share2,
  BookOpen,
  Sparkles,
  ChevronLeft,
  ChevronRight,
  User,
  Calendar
} from 'lucide-react';

const KuralDetail = () => {
  const { number } = useParams();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const { isTamil } = useTheme();

  const [kural, setKural] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isFavorite, setIsFavorite] = useState(false);
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [aiExplanation, setAiExplanation] = useState(null);
  const [loadingAI, setLoadingAI] = useState(false);
  const [showComments, setShowComments] = useState(false);

  useEffect(() => {
    if (number) {
      fetchKural();
      if (showComments) {
        fetchComments();
      }
    }
  }, [number, showComments]);

  const fetchKural = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await kuralAPI.getKural(number);
      setKural(response.data.data);

      // Check if it's in user's favorites
      if (isAuthenticated()) {
        // This would need to be implemented in the API
        // For now, we'll assume it's not favorited
        setIsFavorite(false);
      }
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to fetch kural');
    } finally {
      setLoading(false);
    }
  };

  const fetchComments = async () => {
    try {
      const response = await kuralAPI.getKuralComments(number);
      setComments(response.data.data);
    } catch (err) {
      console.error('Failed to fetch comments:', err);
    }
  };

  const handleFavorite = async () => {
    if (!isAuthenticated()) {
      navigate('/login');
      return;
    }

    try {
      if (isFavorite) {
        await userAPI.removeFavorite(number);
        setIsFavorite(false);
      } else {
        await userAPI.addFavorite(number);
        setIsFavorite(true);
      }
    } catch (err) {
      console.error('Failed to update favorite:', err);
    }
  };

  const handleAddComment = async (e) => {
    e.preventDefault();
    if (!isAuthenticated()) {
      navigate('/login');
      return;
    }

    if (!newComment.trim()) return;

    try {
      await userAPI.addComment({
        kuralNumber: number,
        content: newComment.trim(),
        language: isTamil ? 'tamil' : 'english'
      });
      setNewComment('');
      fetchComments();
    } catch (err) {
      console.error('Failed to add comment:', err);
    }
  };

  const getAIExplanation = async () => {
    try {
      setLoadingAI(true);
      const language = isTamil ? 'tamil' : 'english';
      const response = await chatAPI.getKuralExplanation(number, '', language);
      setAiExplanation(response.data.explanation);
    } catch (err) {
      console.error('Failed to get AI explanation:', err);
    } finally {
      setLoadingAI(false);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${isTamil ? 'குறள்' : 'Kural'} ${number}`,
          text: `${kural.line1} ${kural.line2}`,
          url: window.location.href,
        });
      } catch (err) {
        console.error('Error sharing:', err);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      // You could show a toast notification here
    }
  };

  const navigateKural = (direction) => {
    const newNumber = direction === 'prev'
      ? Math.max(1, parseInt(number) - 1)
      : Math.min(1330, parseInt(number) + 1);
    navigate(`/kural/${newNumber}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" text={isTamil ? 'குறள் ஏற்றுகிறது...' : 'Loading Kural...'} />
      </div>
    );
  }

  if (error || !kural) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">
            {error || (isTamil ? 'குறள் கிடைக்கவில்லை' : 'Kural not found')}
          </div>
          <Link to="/kurals" className="btn-primary btn-md">
            {isTamil ? 'குறள் பட்டியலுக்கு' : 'Back to Kurals'}
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen gradient-bg-vibrant relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-r from-accent-100/15 via-primary-100/10 to-secondary-100/15"></div>
      <div className="absolute top-20 right-20 w-28 h-28 bg-gradient-to-br from-accent-300/20 to-accent-500/20 rounded-full animate-pulse-slow"></div>
      <div className="absolute bottom-20 left-20 w-20 h-20 bg-gradient-to-br from-success-300/20 to-success-500/20 rounded-full animate-pulse-slow" style={{animationDelay: '1s'}}></div>

      <div className="container mx-auto px-4 py-8 relative z-10">
        {/* Navigation */}
        <div className="flex justify-between items-center mb-8">
          <Link to="/kurals" className="btn-ghost btn-md">
            <ArrowLeft className="w-4 h-4 mr-2" />
            {isTamil ? 'பின்செல்' : 'Back'}
          </Link>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => navigateKural('prev')}
              disabled={parseInt(number) <= 1}
              className="btn-outline btn-sm disabled:opacity-50"
            >
              <ChevronLeft className="w-4 h-4" />
            </button>
            <span className="text-sm text-muted-foreground">
              {number} / 1330
            </span>
            <button
              onClick={() => navigateKural('next')}
              disabled={parseInt(number) >= 1330}
              className="btn-outline btn-sm disabled:opacity-50"
            >
              <ChevronRight className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Kural Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Kural Card */}
            <div className="card-vibrant card-glow">
              <div className="card-content p-8 bg-gradient-to-br from-white/95 via-primary-50/30 to-accent-50/20">
                {/* Header */}
                <div className="flex justify-between items-start mb-6">
                  <div>
                    <h1 className="text-3xl font-bold gradient-text-vibrant mb-2 drop-shadow-lg">
                      {isTamil ? 'குறள்' : 'Kural'} {kural.number}
                    </h1>
                    <div className="text-muted-foreground">
                      <div className="font-medium">{kural.adhigaram}</div>
                      <div className="text-sm">{kural.paal}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={handleFavorite}
                      className={`btn-ghost btn-sm ${isFavorite ? 'text-red-500' : ''}`}
                    >
                      <Heart className={`w-5 h-5 ${isFavorite ? 'fill-current' : ''}`} />
                    </button>
                    <button onClick={handleShare} className="btn-ghost btn-sm">
                      <Share2 className="w-5 h-5" />
                    </button>
                  </div>
                </div>

                {/* Kural Text */}
                <div className="text-center mb-8 p-6 bg-gradient-to-r from-accent-50/50 via-primary-50/30 to-secondary-50/50 rounded-xl border border-primary-200/30">
                  <div className="kural-text text-4xl mb-4 bg-gradient-to-r from-primary-700 via-accent-600 to-secondary-700 bg-clip-text text-transparent leading-relaxed font-bold drop-shadow-sm">
                    {kural.line1}<br />
                    {kural.line2}
                  </div>
                  {kural.transliteration && (
                    <div className="text-muted-foreground italic text-lg">
                      {kural.transliteration}
                    </div>
                  )}
                </div>

                {/* Meanings */}
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold mb-2 flex items-center">
                      <BookOpen className="w-4 h-4 mr-2" />
                      {isTamil ? 'தமிழ் பொருள்' : 'Tamil Meaning'}
                    </h3>
                    <p className="kural-meaning">{kural.meaning_ta}</p>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-2 flex items-center">
                      <BookOpen className="w-4 h-4 mr-2" />
                      {isTamil ? 'ஆங்கில பொருள்' : 'English Meaning'}
                    </h3>
                    <p className="kural-meaning">{kural.meaning_en}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Commentaries */}
            <div className="card">
              <div className="card-header">
                <h2 className="card-title">
                  {isTamil ? 'உரைகள்' : 'Commentaries'}
                </h2>
              </div>
              <div className="card-content space-y-6">
                {/* Parimelalhagar Commentary */}
                <div>
                  <h3 className="font-semibold mb-2 text-primary-600">
                    {isTamil ? 'பரিமேலழகர் உரை' : 'Parimelazhagar Commentary'}
                  </h3>
                  <p className="kural-commentary">{kural.commentary_parimel}</p>
                </div>

                {/* Karunanidhi Commentary */}
                {kural.commentary_karunanidhi && (
                  <div>
                    <h3 className="font-semibold mb-2 text-primary-600">
                      {isTamil ? 'கருணாநிதி உரை' : 'Karunanidhi Commentary'}
                    </h3>
                    <p className="kural-commentary">{kural.commentary_karunanidhi}</p>
                  </div>
                )}

                {/* Yogi Commentary */}
                {kural.commentary_yogi && (
                  <div>
                    <h3 className="font-semibold mb-2 text-primary-600">
                      {isTamil ? 'யோகி உரை' : 'Yogi Commentary'}
                    </h3>
                    <p className="kural-commentary">{kural.commentary_yogi}</p>
                  </div>
                )}
              </div>
            </div>

            {/* AI Explanation */}
            <div className="card-vibrant card-glow bg-gradient-to-br from-accent-50/80 via-primary-50/60 to-secondary-50/80">
              <div className="card-header">
                <div className="flex justify-between items-center">
                  <h2 className="card-title flex items-center">
                    <Sparkles className="w-5 h-5 mr-2 text-primary-600" />
                    {isTamil ? 'AI விளக்கம்' : 'AI Explanation'}
                  </h2>
                  {!aiExplanation && (
                    <button
                      onClick={getAIExplanation}
                      disabled={loadingAI}
                      className="btn-primary btn-sm"
                    >
                      {loadingAI ? (
                        <LoadingSpinner size="sm" />
                      ) : (
                        isTamil ? 'விளக்கம் பெறு' : 'Get Explanation'
                      )}
                    </button>
                  )}
                </div>
              </div>
              {aiExplanation && (
                <div className="card-content">
                  <div className="prose max-w-none">
                    {aiExplanation.split('\n').map((paragraph, index) => (
                      <p key={index} className="mb-3 leading-relaxed">{paragraph}</p>
                    ))}
                  </div>

                  {/* Show fallback message if AI is not available */}
                  <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                    <div className="flex items-center text-sm text-amber-700">
                      <Sparkles className="w-4 h-4 mr-2" />
                      {isTamil
                        ? 'AI சேவை தற்போது கிடைக்கவில்லை. இது ஒரு அடிப்படை விளக்கம். முழு AI சக்தியுடன் கூடிய ஆழமான நுண்ணறிவுகளுக்கு, AI சேவையை சரியாக கட்டமைக்கவும்.'
                        : 'AI service is currently unavailable. This is a basic explanation. For deeper AI-powered insights, please ensure the AI service is properly configured.'
                      }
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <div className="card">
              <div className="card-header">
                <h3 className="card-title text-lg">
                  {isTamil ? 'செயல்கள்' : 'Actions'}
                </h3>
              </div>
              <div className="card-content space-y-3">
                <Link to="/chat" className="btn-primary btn-md w-full">
                  <MessageCircle className="w-4 h-4 mr-2" />
                  {isTamil ? 'வள்ளுவருடன் பேசு' : 'Chat with Valluvar'}
                </Link>

                <button
                  onClick={() => setShowComments(!showComments)}
                  className="btn-outline btn-md w-full"
                >
                  <MessageCircle className="w-4 h-4 mr-2" />
                  {isTamil ? 'கருத்துகள்' : 'Comments'} ({comments.length})
                </button>
              </div>
            </div>

            {/* Related Kurals */}
            <div className="card">
              <div className="card-header">
                <h3 className="card-title text-lg">
                  {isTamil ? 'தொடர்புடைய குறள்கள்' : 'Related Kurals'}
                </h3>
              </div>
              <div className="card-content">
                <div className="space-y-2">
                  <Link
                    to={`/adhigaram/${encodeURIComponent(kural.adhigaram)}`}
                    className="block p-3 rounded-md hover:bg-accent transition-colors"
                  >
                    <div className="font-medium text-sm">{kural.adhigaram}</div>
                    <div className="text-xs text-muted-foreground">
                      {isTamil ? 'இந்த அதிகாரத்தின் மற்ற குறள்கள்' : 'Other kurals in this chapter'}
                    </div>
                  </Link>

                  <Link
                    to={`/category/${kural.paal}`}
                    className="block p-3 rounded-md hover:bg-accent transition-colors"
                  >
                    <div className="font-medium text-sm">{kural.paal}</div>
                    <div className="text-xs text-muted-foreground">
                      {isTamil ? 'இந்த பாலின் அனைத்து குறள்கள்' : 'All kurals in this section'}
                    </div>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Comments Section */}
        {showComments && (
          <div className="mt-8">
            <div className="card">
              <div className="card-header">
                <h3 className="card-title">
                  {isTamil ? 'கருத்துகள்' : 'Comments'}
                </h3>
              </div>
              <div className="card-content">
                {/* Add Comment Form */}
                {isAuthenticated() ? (
                  <form onSubmit={handleAddComment} className="mb-6">
                    <textarea
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      placeholder={isTamil ? 'உங்கள் கருத்தை பகிருங்கள்...' : 'Share your thoughts...'}
                      className="textarea w-full mb-3"
                      rows="3"
                    />
                    <button type="submit" className="btn-primary btn-md">
                      {isTamil ? 'கருத்து சேர்' : 'Add Comment'}
                    </button>
                  </form>
                ) : (
                  <div className="mb-6 p-4 bg-muted rounded-md text-center">
                    <p className="mb-3">
                      {isTamil ? 'கருத்து சேர்க்க உள்நுழையவும்' : 'Login to add comments'}
                    </p>
                    <Link to="/login" className="btn-primary btn-md">
                      {isTamil ? 'உள்நுழை' : 'Login'}
                    </Link>
                  </div>
                )}

                {/* Comments List */}
                <div className="space-y-4">
                  {comments.map((comment) => (
                    <div key={comment._id} className="border-l-4 border-primary-200 pl-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <User className="w-4 h-4 text-muted-foreground" />
                        <span className="font-medium text-sm">{comment.user.username}</span>
                        <Calendar className="w-3 h-3 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground">
                          {new Date(comment.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-sm">{comment.content}</p>
                    </div>
                  ))}

                  {comments.length === 0 && (
                    <p className="text-center text-muted-foreground">
                      {isTamil ? 'இன்னும் கருத்துகள் இல்லை' : 'No comments yet'}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default KuralDetail;
