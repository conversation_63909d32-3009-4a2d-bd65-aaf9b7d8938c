import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { useTheme } from '../hooks/useTheme';
import {
  Menu,
  X,
  Search,
  Sun,
  Moon,
  Globe,
  User,
  Heart,
  LogOut,
  Settings,
  ChevronDown
} from 'lucide-react';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const { user, logout, isAuthenticated } = useAuth();
  const { toggleTheme, toggleLanguage, isDark, isTamil } = useTheme();
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogout = async () => {
    await logout();
    setIsUserMenuOpen(false);
    navigate('/');
  };

  const handleSearch = (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const query = formData.get('search');
    if (query.trim()) {
      navigate(`/search?q=${encodeURIComponent(query.trim())}`);
    }
  };

  const isActivePath = (path) => location.pathname === path;

  const navLinks = [
    { path: '/', label: isTamil ? 'முகப்பு' : 'Home' },
    { path: '/kurals', label: isTamil ? 'குறள்கள்' : 'Kurals' },
  ];

  const categoryLinks = [
    { path: `/category/${encodeURIComponent('அறம்')}`, label: isTamil ? 'அறம்' : 'Virtue' },
    { path: `/category/${encodeURIComponent('பொருள்')}`, label: isTamil ? 'பொருள்' : 'Wealth' },
    { path: `/category/${encodeURIComponent('இன்பம்')}`, label: isTamil ? 'இன்பம்' : 'Love' }
  ];

  return (
    <header className="sticky top-0 z-50 w-full bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/80 border-b shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-2">
          {/* Left: Logo and Categories */}
          <div className="flex items-center space-x-4">
            <Link to="/" className="text-xl font-bold gradient-text-heritage">
              {isTamil ? 'குறள்வெர்ஸ்' : 'KuralVerse'}
            </Link>
            <div className="hidden md:flex space-x-3 border-l pl-4 ml-2">
              {categoryLinks.map(link => (
                <Link
                  key={link.path}
                  to={link.path}
                  className={`text-sm font-semibold px-3 py-1 rounded-md transition ${
                    isActivePath(link.path) ? 'bg-primary-100 text-primary-800' : 'text-muted-foreground hover:bg-accent'
                  }`}
                >
                  {link.label}
                </Link>
              ))}
            </div>
          </div>

          {/* Center: Search */}
          <div className="hidden lg:flex flex-1 justify-center">
            <form onSubmit={handleSearch} className="relative w-96">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <input
                type="text"
                name="search"
                placeholder={isTamil ? 'குறள் தேடு...' : 'Search...'}
                className="input pl-10 pr-4 py-2 w-full rounded-md border border-gray-300 focus:ring-primary-500 focus:border-primary-500"
              />
            </form>
          </div>

          {/* Right Controls */}
          <div className="flex items-center space-x-2">
            <button onClick={toggleTheme} className="btn-ghost btn-sm" aria-label="Toggle theme">
              {isDark ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
            </button>
            <button onClick={toggleLanguage} className="btn-ghost btn-sm" aria-label="Toggle language">
              <Globe className="w-4 h-4" />
              <span className="ml-1 text-xs">{isTamil ? 'EN' : 'தமிழ்'}</span>
            </button>

            {isAuthenticated() ? (
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="btn-ghost btn-sm flex items-center space-x-1"
                >
                  <User className="w-4 h-4" />
                  <span className="text-sm font-medium hidden sm:inline">
                    {user?.username || (isTamil ? 'சுயவிவரம்' : 'Profile')}
                  </span>
                  <ChevronDown className="w-4 h-4" />
                </button>
                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                    <Link to="/profile" className="block px-4 py-2 text-sm hover:bg-gray-100" onClick={() => setIsUserMenuOpen(false)}>
                      {isTamil ? 'சுயவிவரம்' : 'Profile'}
                    </Link>
                    <Link to="/favorites" className="block px-4 py-2 text-sm hover:bg-gray-100" onClick={() => setIsUserMenuOpen(false)}>
                      {isTamil ? 'விருப்பங்கள்' : 'Favorites'}
                    </Link>
                    <hr className="my-1 border-gray-200" />
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 text-sm hover:bg-red-100 text-red-600"
                    >
                      {isTamil ? 'வெளியேறு' : 'Logout'}
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link to="/login" className="btn-ghost btn-sm">{isTamil ? 'உள்நுழை' : 'Login'}</Link>
                <Link to="/register" className="btn-primary btn-sm">{isTamil ? 'பதிவு செய்' : 'Register'}</Link>
              </div>
            )}

            <button onClick={() => setIsMenuOpen(!isMenuOpen)} className="md:hidden btn-ghost btn-sm" aria-label="Toggle menu">
              {isMenuOpen ? <X className="w-4 h-4" /> : <Menu className="w-4 h-4" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden border-t py-4">
            <form onSubmit={handleSearch} className="mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <input
                  type="text"
                  name="search"
                  placeholder={isTamil ? 'குறள் தேடு...' : 'Search...'}
                  className="input pl-10 pr-4 py-2 w-full"
                />
              </div>
            </form>
            <nav className="space-y-2">
              {[...navLinks, ...categoryLinks].map(link => (
                <Link
                  key={link.path}
                  to={link.path}
                  className={`block px-3 py-2 rounded-md text-sm font-medium ${
                    isActivePath(link.path) ? 'bg-primary-100 text-primary-600' : 'text-muted-foreground hover:bg-accent'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {link.label}
                </Link>
              ))}
            </nav>
          </div>
        )}
      </div>
      {isUserMenuOpen && <div className="fixed inset-0 z-40" onClick={() => setIsUserMenuOpen(false)} />}
    </header>
  );
};

export default Header;