import React, { useState, useEffect, useRef } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useTheme } from '../hooks/useTheme';
import { useAuth } from '../hooks/useAuth';
import {
  User,
  LogOut,
  Heart,
  Settings,
  Menu,
  X,
  ChevronDown
} from 'lucide-react';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const { isTamil, toggleLanguage } = useTheme();
  const { user, isAuthenticated, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const profileRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (profileRef.current && !profileRef.current.contains(event.target)) {
        setIsProfileOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    logout();
    setIsProfileOpen(false);
    navigate('/');
  };

  const navItems = [
    { name: 'அறம்', nameEn: 'Virtue', path: '/category/அறம்' },
    { name: 'பொருள்', nameEn: 'Wealth', path: '/category/பொருள்' },
    { name: 'இன்பம்', nameEn: 'Love', path: '/category/இன்பம்' }
  ];

  const isActive = (path) => location.pathname === path;

  return (
    <header className="sticky top-0 z-50 w-full border-b border-heritage-200/30 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/80 shadow-sm">
      <div className="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">

          {/* Left - Logo */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-heritage-600 to-heritage-700 rounded-lg flex items-center justify-center border border-heritage-500/30 shadow-md">
              <span className="text-heritage-50 font-bold text-sm anek-tamil-bold">குறள்</span>
            </div>
            <Link to="/" className={`text-2xl font-bold bg-gradient-to-r from-heritage-600 via-heritage-700 to-temple-700 bg-clip-text text-transparent tracking-wider ${isTamil ? 'anek-tamil-bold' : 'font-english'}`}>
              {isTamil ? 'குறள்வெர்ஸ்' : 'KuralVerse'}
            </Link>
          </div>

          {/* Center - Nav Links */}
          <nav className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 border border-transparent ${
                  isActive(item.path)
                    ? 'text-heritage-700 bg-heritage-50 border-heritage-200/50 shadow-sm'
                    : 'text-temple-600 hover:text-heritage-700 hover:bg-heritage-50/50 hover:border-heritage-200/30'
                } ${isTamil ? 'anek-tamil-medium' : 'font-english'}`}
              >
                {isTamil ? item.name : item.nameEn}
              </Link>
            ))}
          </nav>

          {/* Right - Profile/Language */}
          <div className="flex items-center space-x-4">
            {/* Language Toggle */}
            <button
              onClick={toggleLanguage}
              className={`hidden sm:flex items-center px-3 py-2 rounded-md text-sm font-medium text-temple-600 hover:text-heritage-700 hover:bg-heritage-50/50 transition-all duration-200 border border-transparent hover:border-heritage-200/30 ${!isTamil ? 'anek-tamil-medium' : 'font-english'}`}
            >
              {isTamil ? 'EN' : 'தமிழ்'}
            </button>

            {/* Auth section */}
            {isAuthenticated() ? (
              <div className="relative" ref={profileRef}>
                <button
                  onClick={() => setIsProfileOpen(!isProfileOpen)}
                  className="flex items-center space-x-2 px-3 py-2 rounded-full border border-transparent hover:bg-heritage-50/50 hover:border-heritage-200/30 transition"
                >
                  <div className="w-8 h-8 bg-gradient-to-br from-heritage-600 to-heritage-700 rounded-full flex items-center justify-center shadow-sm">
                    <User className="w-4 h-4 text-white" />
                  </div>
                  <span className="hidden sm:block text-sm font-medium text-temple-700 max-w-24 truncate">
                    {user?.name || user?.email}
                  </span>
                  <ChevronDown className={`w-4 h-4 text-temple-600 ${isProfileOpen ? 'rotate-180' : ''} transition-transform`} />
                </button>

                {isProfileOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-heritage-200/50 py-2 z-50">
                    <Link
                      to="/profile"
                      className={`flex items-center px-4 py-2 text-sm text-temple-700 hover:bg-heritage-50 ${isTamil ? 'anek-tamil-default' : 'font-english'}`}
                      onClick={() => setIsProfileOpen(false)}
                    >
                      <Settings className="w-4 h-4 mr-2" />
                      {isTamil ? 'சுயவிவரம்' : 'Profile'}
                    </Link>
                    <Link
                      to="/favorites"
                      className={`flex items-center px-4 py-2 text-sm text-temple-700 hover:bg-heritage-50 ${isTamil ? 'anek-tamil-default' : 'font-english'}`}
                      onClick={() => setIsProfileOpen(false)}
                    >
                      <Heart className="w-4 h-4 mr-2" />
                      {isTamil ? 'விருப்பங்கள்' : 'Favorites'}
                    </Link>
                    <hr className="my-2 border-heritage-200/50" />
                    <button
                      onClick={handleLogout}
                      className={`flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 ${isTamil ? 'anek-tamil-default' : 'font-english'}`}
                    >
                      <LogOut className="w-4 h-4 mr-2" />
                      {isTamil ? 'வெளியேறு' : 'Logout'}
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <Link
                  to="/login"
                  className={`px-4 py-2 text-sm font-medium text-temple-600 hover:text-heritage-700 hover:bg-heritage-50/50 rounded-md transition-all duration-200 border border-transparent hover:border-heritage-200/30 ${isTamil ? 'anek-tamil-default' : 'font-english'}`}
                >
                  {isTamil ? 'உள்நுழை' : 'Login'}
                </Link>
                <Link
                  to="/register"
                  className={`px-4 py-2 text-sm font-medium bg-gradient-to-r from-heritage-600 to-heritage-700 text-heritage-50 rounded-md hover:from-heritage-700 hover:to-heritage-800 transition-all duration-200 shadow-sm hover:shadow-md border border-heritage-500/30 ${isTamil ? 'anek-tamil-medium' : 'font-english'}`}
                >
                  {isTamil ? 'பதிவு செய்' : 'Register'}
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
