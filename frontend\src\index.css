@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Anek+Tamil&family=Noto+Sans+Tamil:wght@100..900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base font family for English */
body {
  font-family: 'Inter', sans-serif;
}

/* Tamil font class */
.tamil-font {
  font-family: 'Noto Sans Tamil', sans-serif;
}


body {
  font-family: 'Inter', sans-serif;
}

/* Tamil font with Anek Tamil */
.font-tamil {
  font-family: "Anek Tamil", "Noto Sans Tamil", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}

/* Anek Tamil variable style class */
.anek-tamil-default {
  font-family: "Anek Tamil", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}

.anek-tamil-medium {
  font-family: "Anek Tamil", sans-serif;
  font-optical-sizing: auto;
  font-weight: 500;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}

.anek-tamil-semibold {
  font-family: "Anek Tamil", sans-serif;
  font-optical-sizing: auto;
  font-weight: 600;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}

.anek-tamil-bold {
  font-family: "Anek Tamil", sans-serif;
  font-optical-sizing: auto;
  font-weight: 700;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}

.font-english {
  font-family: 'Inter', sans-serif;
}

/* Traditional Tamil Manuscript Style */
.font-manuscript {
  font-family: "Playfair Display", "Cormorant Garamond", serif;
  font-weight: 500;
  letter-spacing: 0.025em;
}

.font-traditional {
  font-family: "Catamaran", "Anek Tamil", serif;
  font-weight: 400;
}

/* Authentic Tamil Kural Box Styling */
.kural-box {
  background: linear-gradient(135deg, #fffaf0 0%, #fef5e7 100%);
  border: 2px solid #d4a373;
  border-radius: 12px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
}

.kural-box::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, #f97316, #ea580c, #c2410c);
  border-radius: 12px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.kural-box:hover::before {
  opacity: 0.1;
}

/* Tamil Ornamental Divider */
.tamil-divider {
  background: linear-gradient(90deg, transparent, #d4a373, transparent);
  height: 2px;
  position: relative;
}

.tamil-divider::before {
  content: '❋';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: #fffaf0;
  color: #d4a373;
  padding: 0 8px;
  font-size: 16px;
}

/* Heritage Gradient Text */
.gradient-text-heritage {
  background: linear-gradient(135deg, #d4a373 0%, #b8956a  50%, #9c8761 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-saffron {
  background: linear-gradient(135deg, #f97316 0%, #ea580c 50%, #c2410c 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-royal {
  background: linear-gradient(135deg, #a855f7 0%, #9333ea 50%, #7c3aed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Heritage theme gradient text */
.gradient-text-heritage {
  background: linear-gradient(45deg, #e85d1c, #d18f2e, #b8751a, #374151);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 6s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}
