@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Tamil:wght@300;400;500;600;700&display=swap');

@layer base {
  :root {
    --color-primary: 237 118 17;
    --color-secondary: 14 165 233;
    --color-accent: 251 146 60;
    --color-success: 34 197 94;
    --color-warning: 245 158 11;
    --color-error: 239 68 68;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-english;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  .font-tamil {
    font-family: 'Noto Sans Tamil', sans-serif;
  }

  .font-english {
    font-family: 'Inter', sans-serif;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }

  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 active:bg-secondary-800;
  }

  .btn-outline {
    @apply btn border border-input bg-background hover:bg-accent hover:text-accent-foreground;
  }

  .btn-ghost {
    @apply btn hover:bg-accent hover:text-accent-foreground;
  }

  .btn-sm {
    @apply h-9 px-3 text-xs;
  }

  .btn-md {
    @apply h-10 py-2 px-4;
  }

  .btn-lg {
    @apply h-11 px-8;
  }

  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm text-muted-foreground;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .textarea {
    @apply flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .kural-text {
    @apply font-tamil text-lg leading-relaxed;
  }

  .kural-meaning {
    @apply text-gray-700 dark:text-gray-300 leading-relaxed;
  }

  .kural-commentary {
    @apply text-sm text-gray-600 dark:text-gray-400 leading-relaxed;
  }

  .gradient-bg {
    @apply bg-gradient-to-br from-primary-50 via-white to-secondary-50;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
