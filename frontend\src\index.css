@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Tamil:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Tamil:wght@400;700&family=Inter:wght@400;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base font family for English */
body {
  font-family: 'Inter', sans-serif;
}

/* Tamil font class */
.tamil-font {
  font-family: 'Noto Sans Tamil', sans-serif;
}


body {
  font-family: 'Inter', sans-serif;
}

.font-tamil {
  font-family: 'Noto Sans Tamil', sans-serif;
}

.font-english {
  font-family: 'Inter', sans-serif;
}

/* Heritage theme gradient text */
.gradient-text-heritage {
  background: linear-gradient(45deg, #e85d1c, #d18f2e, #b8751a, #374151);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 6s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}
