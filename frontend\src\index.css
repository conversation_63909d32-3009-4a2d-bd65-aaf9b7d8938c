@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Anek+Tamil&family=Noto+Sans+Tamil:wght@100..900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base font family for English */
body {
  font-family: 'Inter', sans-serif;
}

/* Tamil font class */
.tamil-font {
  font-family: 'Noto Sans Tamil', sans-serif;
}


body {
  font-family: 'Inter', sans-serif;
}

/* Tamil font with Anek Tamil */
.font-tamil {
  font-family: "Anek Tamil", "Noto Sans Tamil", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}

/* Anek Tamil variable style class */
.anek-tamil-default {
  font-family: "Anek Tamil", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}

.anek-tamil-medium {
  font-family: "Anek Tamil", sans-serif;
  font-optical-sizing: auto;
  font-weight: 500;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}

.anek-tamil-semibold {
  font-family: "Anek Tamil", sans-serif;
  font-optical-sizing: auto;
  font-weight: 600;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}

.anek-tamil-bold {
  font-family: "Anek Tamil", sans-serif;
  font-optical-sizing: auto;
  font-weight: 700;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}

.font-english {
  font-family: 'Inter', sans-serif;
}

/* Heritage theme gradient text */
.gradient-text-heritage {
  background: linear-gradient(45deg, #e85d1c, #d18f2e, #b8751a, #374151);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 6s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}
