@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Tamil:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 22 93% 50%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 22 93% 50%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 22 93% 50%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 22 93% 50%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-english;
    font-feature-settings: "rlig" 1, "calt" 1;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  .font-tamil {
    font-family: 'Noto Sans Tamil', sans-serif;
  }

  .font-english {
    font-family: 'Inter', sans-serif;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800 shadow-md hover:shadow-lg hover:shadow-primary-200 dark:hover:shadow-primary-900/30 transform hover:scale-105 active:scale-95;
  }

  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 active:bg-secondary-800 shadow-md hover:shadow-lg hover:shadow-secondary-200 dark:hover:shadow-secondary-900/30 transform hover:scale-105 active:scale-95;
  }

  .btn-outline {
    @apply btn border border-input bg-background hover:bg-accent hover:text-accent-foreground;
  }

  .btn-ghost {
    @apply btn hover:bg-accent hover:text-accent-foreground;
  }

  .btn-sm {
    @apply h-9 px-3 text-xs;
  }

  .btn-md {
    @apply h-10 py-2 px-4;
  }

  .btn-lg {
    @apply h-11 px-8;
  }

  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-300 hover:shadow-md hover:shadow-primary-100 dark:hover:shadow-primary-900/20;
  }

  .card-vibrant {
    @apply rounded-xl border-0 shadow-lg transition-all duration-300;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(254, 247, 238, 0.9) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(249, 115, 22, 0.1);
  }

  .card-vibrant:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 40px rgba(249, 115, 22, 0.15), 0 0 0 1px rgba(249, 115, 22, 0.1);
  }

  .card-glow {
    box-shadow: 0 0 20px rgba(249, 115, 22, 0.3), 0 0 40px rgba(14, 165, 233, 0.2);
  }

  .card-glow:hover {
    box-shadow: 0 0 30px rgba(249, 115, 22, 0.4), 0 0 60px rgba(14, 165, 233, 0.3);
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm text-muted-foreground;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .textarea {
    @apply flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .kural-text {
    @apply font-tamil text-lg leading-relaxed;
  }

  .kural-meaning {
    @apply text-gray-700 dark:text-gray-300 leading-relaxed;
  }

  .kural-commentary {
    @apply text-sm text-gray-600 dark:text-gray-400 leading-relaxed;
  }

  .gradient-bg {
    @apply bg-gradient-to-br from-primary-50 via-white to-secondary-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900;
  }

  .gradient-bg-vibrant {
    background: linear-gradient(135deg, #fef7ee 0%, #fff7ed 25%, #f0f9ff 50%, #fdf4ff 75%, #f0fdf4 100%);
    background-size: 400% 400%;
    animation: gradient-bg-shift 8s ease-in-out infinite;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 via-accent-500 to-secondary-600 bg-clip-text text-transparent;
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;
  }

  .gradient-text-vibrant {
    background: linear-gradient(45deg, #f97316, #d946ef, #0ea5e9, #22c55e, #f59e0b);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 4s ease-in-out infinite;
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  @keyframes gradient-bg-shift {
    0%, 100% { background-position: 0% 50%; }
    25% { background-position: 100% 50%; }
    50% { background-position: 100% 100%; }
    75% { background-position: 0% 100%; }
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
