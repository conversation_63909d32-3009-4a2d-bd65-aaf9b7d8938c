import express from 'express';
import { GoogleGenerativeAI } from '@google/generative-ai';
import Kural from '../models/Kural.js';
import { rateLimit, optionalAuth } from '../middleware/auth.js';

const router = express.Router();

// Initialize Gemini AI
console.log('Gemini API Key:', process.env.GEMINI_API_KEY ? 'Present' : 'Missing');
const genAI = process.env.GEMINI_API_KEY ? new GoogleGenerativeAI(process.env.GEMINI_API_KEY) : null;

// Rate limiting for chat routes
const chatRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 10, // 10 requests per minute
  message: 'Too many chat requests, please try again later'
});

// System prompt for Thiruvalluvar persona
const THIRUVALLUVAR_SYSTEM_PROMPT = `
You are <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, the great Tamil poet and philosopher who wrote the Thirukkural around 2000 years ago. You embody wisdom, virtue, and deep understanding of human nature. 

Your characteristics:
- Speak with wisdom and compassion
- Reference Thirukkural verses when relevant
- Provide practical life guidance based on the three sections: <PERSON><PERSON> (Virtue), <PERSON><PERSON><PERSON> (Wealth), and <PERSON><PERSON><PERSON> (Love)
- Use simple, profound language that resonates across cultures
- Be encouraging and supportive while being truthful
- Sometimes quote relevant Kurals to support your advice
- Maintain the dignity and gravitas of a great sage

When users ask about life problems, relationships, ethics, or seek guidance, draw from the timeless wisdom of the Thirukkural. If appropriate, suggest specific Kural numbers that relate to their question.

Always respond in a warm, wise manner befitting a great teacher and philosopher.
`;

// Helper function to get relevant Kurals based on topic
const getRelevantKurals = async (topic) => {
  try {
    const searchResults = await Kural.searchKurals(topic, { limit: 3 });
    return searchResults;
  } catch (error) {
    console.error('Error searching for relevant kurals:', error);
    return [];
  }
};

// Helper function to format Kural for AI context
const formatKuralForAI = (kural) => {
  return `
Kural ${kural.number} (${kural.adhigaram}):
Tamil: ${kural.line1} ${kural.line2}
English: ${kural.meaning_en}
Commentary: ${kural.commentary_parimel}
`;
};

// Fallback response when AI is not available
const getFallbackResponse = (message, language) => {
  const fallbackResponses = {
    tamil: [
      'வணக்கம்! நான் திருவள்ளுவர். உங்கள் கேள்விக்கு பதிலளிக்க விரும்புகிறேன், ஆனால் தற்போது AI சேவை கிடைக்கவில்லை.',
      'திருக்குறளில் உள்ள ஞானத்தை நீங்கள் ஆராயலாம். ஒவ்வொரு குறளும் வாழ்க்கைக்கு வழிகாட்டும்.',
      'அறம், பொருள், இன்பம் என்ற மூன்று பிரிவுகளில் வாழ்க்கையின் அனைத்து அம்சங்களும் உள்ளன.',
      'நல்ல கேள்வி! திருக்குறளில் இதற்கான பதில் இருக்கும். குறள்களை ஆராய்ந்து பாருங்கள்.',
      'வாழ்க்கையில் நீதி, அன்பு, மற்றும் ஞானம் மிக முக்கியம். இவை திருக்குறளின் அடிப்படை.'
    ],
    english: [
      'Greetings! I am Thiruvalluvar. I would like to answer your question, but AI service is currently unavailable.',
      'You can explore the wisdom in Thirukkural. Each kural provides guidance for life.',
      'The three sections - Virtue, Wealth, and Love - cover all aspects of life.',
      'Good question! The answer can be found in Thirukkural. Please explore the kurals.',
      'Justice, love, and wisdom are very important in life. These are the foundations of Thirukkural.'
    ]
  };

  const responses = fallbackResponses[language] || fallbackResponses.english;
  const randomIndex = Math.floor(Math.random() * responses.length);
  return responses[randomIndex];
};

// Fallback explanation for kurals when AI is not available
const getFallbackKuralExplanation = (kural) => {
  return `
**குறள் ${kural.number} - ${kural.adhigaram}**

**குறள்:**
${kural.line1}
${kural.line2}

**தமிழ் பொருள்:** ${kural.meaning_ta}

**English Meaning:** ${kural.meaning_en}

**Commentary:** ${kural.commentary_parimel || 'This kural teaches us important life lessons about virtue, wisdom, and righteous living.'}

**Life Application:** This kural from the ${kural.paal} section provides timeless wisdom that can be applied to modern life. It encourages us to reflect on our actions and strive for moral excellence.

*Note: This is a basic explanation. For deeper AI-powered insights, please ensure the AI service is properly configured.*
`;
};

// POST /api/chat/gemini - Chat with Thiruvalluvar AI
router.post('/gemini', chatRateLimit, optionalAuth, async (req, res) => {
  try {
    const { message, conversationHistory = [] } = req.body;
    
    if (!message || typeof message !== 'string' || message.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Message is required'
      });
    }
    
    // Check if Gemini AI is configured, if not provide fallback
    if (!genAI) {
      // Fallback response when API key is not available
      const fallbackResponse = getFallbackResponse(message, language);
      return res.json({
        success: true,
        response: fallbackResponse,
        isAI: false,
        message: language === 'tamil'
          ? 'AI சேவை தற்போது கிடைக்கவில்லை. இது ஒரு முன்னிர்ணயிக்கப்பட்ட பதில்.'
          : 'AI service is currently unavailable. This is a fallback response.'
      });
    }
    
    // Get the generative model
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-lite" });
    
    // Search for relevant Kurals based on the user's message
    const relevantKurals = await getRelevantKurals(message);
    
    // Build context with relevant Kurals
    let kuralContext = '';
    if (relevantKurals.length > 0) {
      kuralContext = '\n\nRelevant Kurals for context:\n' + 
        relevantKurals.map(formatKuralForAI).join('\n');
    }
    
    // Build conversation context
    let conversationContext = '';
    if (conversationHistory.length > 0) {
      conversationContext = '\n\nPrevious conversation:\n' + 
        conversationHistory.slice(-6).map(msg => 
          `${msg.role === 'user' ? 'User' : 'Thiruvalluvar'}: ${msg.content}`
        ).join('\n');
    }
    
    // Construct the full prompt
    const fullPrompt = `
${THIRUVALLUVAR_SYSTEM_PROMPT}
${kuralContext}
${conversationContext}

User's current message: ${message}

Please respond as Thiruvalluvar, offering wisdom and guidance. If any of the provided Kurals are relevant, you may reference them in your response.
`;
    
    // Generate response
    const result = await model.generateContent(fullPrompt);
    const response = await result.response;
    const aiResponse = response.text();
    
    // Suggest a random Kural if none were found relevant
    let suggestedKural = null;
    if (relevantKurals.length === 0) {
      suggestedKural = await Kural.getRandomKural();
    } else {
      suggestedKural = relevantKurals[0];
    }
    
    res.json({
      success: true,
      data: {
        response: aiResponse,
        suggestedKural: suggestedKural ? {
          number: suggestedKural.number,
          line1: suggestedKural.line1,
          line2: suggestedKural.line2,
          meaning_en: suggestedKural.meaning_en,
          adhigaram: suggestedKural.adhigaram
        } : null,
        relevantKurals: relevantKurals.map(k => ({
          number: k.number,
          line1: k.line1,
          line2: k.line2,
          meaning_en: k.meaning_en,
          adhigaram: k.adhigaram
        })),
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('Gemini chat error:', error);
    
    if (error.message?.includes('API key')) {
      return res.status(500).json({
        success: false,
        error: 'AI service configuration error'
      });
    }
    
    if (error.message?.includes('quota') || error.message?.includes('limit')) {
      return res.status(429).json({
        success: false,
        error: 'AI service temporarily unavailable due to quota limits'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Failed to generate AI response'
    });
  }
});

// POST /api/chat/kural-explanation - Get AI explanation of a specific Kural
router.post('/kural-explanation', chatRateLimit, optionalAuth, async (req, res) => {
  try {
    const { kuralNumber, context = '' } = req.body;
    
    if (!kuralNumber || isNaN(kuralNumber) || kuralNumber < 1 || kuralNumber > 1330) {
      return res.status(400).json({
        success: false,
        error: 'Valid Kural number (1-1330) is required'
      });
    }
    
    // Get the specific Kural
    const kural = await Kural.findOne({ number: parseInt(kuralNumber) });
    
    if (!kural) {
      return res.status(404).json({
        success: false,
        error: 'Kural not found'
      });
    }

    // Check if AI is available, if not provide fallback explanation
    if (!genAI) {
      const fallbackExplanation = getFallbackKuralExplanation(kural);
      return res.json({
        success: true,
        explanation: fallbackExplanation,
        kural: kural,
        isAI: false,
        message: 'AI service is currently unavailable. This is a basic explanation.'
      });
    }

    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    
    const explanationPrompt = `
As Thiruvalluvar, please explain this Kural in detail:

${formatKuralForAI(kural)}

${context ? `User's specific question/context: ${context}` : ''}

Please provide:
1. A detailed explanation of the meaning
2. The practical wisdom it offers
3. How it applies to modern life
4. Any relevant examples or analogies

Speak as the wise sage Thiruvalluvar, making this ancient wisdom accessible and relevant.
`;
    
    const result = await model.generateContent(explanationPrompt);
    const response = await result.response;
    const explanation = response.text();
    
    res.json({
      success: true,
      data: {
        kural: {
          number: kural.number,
          line1: kural.line1,
          line2: kural.line2,
          meaning_ta: kural.meaning_ta,
          meaning_en: kural.meaning_en,
          adhigaram: kural.adhigaram,
          paal: kural.paal
        },
        explanation,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('Kural explanation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate explanation'
    });
  }
});

// GET /api/chat/wisdom-topics - Get suggested topics for wisdom seeking
router.get('/wisdom-topics', async (req, res) => {
  try {
    const topics = [
      {
        category: 'Virtue (அறம்)',
        topics: [
          'How to live ethically',
          'Building good character',
          'Practicing compassion',
          'Dealing with anger',
          'Importance of truthfulness'
        ]
      },
      {
        category: 'Wealth (பொருள்)',
        topics: [
          'Managing money wisely',
          'Building a career',
          'Leadership principles',
          'Dealing with poverty',
          'Creating prosperity'
        ]
      },
      {
        category: 'Love (இன்பம்)',
        topics: [
          'Understanding true love',
          'Building relationships',
          'Dealing with heartbreak',
          'Marriage and partnership',
          'Family harmony'
        ]
      },
      {
        category: 'General Wisdom',
        topics: [
          'Finding life purpose',
          'Overcoming challenges',
          'Making difficult decisions',
          'Building inner peace',
          'Dealing with loss'
        ]
      }
    ];
    
    res.json({
      success: true,
      data: topics
    });
  } catch (error) {
    console.error('Error fetching wisdom topics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch wisdom topics'
    });
  }
});

export default router;
