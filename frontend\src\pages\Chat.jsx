import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { useTheme } from '../hooks/useTheme';
import { chatAPI } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';
import {
  Send,
  Sparkles,
  BookOpen,
  MessageCircle,
  User,
  Bot,
  Lightbulb,
  RefreshCw
} from 'lucide-react';

const Chat = () => {
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [wisdomTopics, setWisdomTopics] = useState([]);
  const messagesEndRef = useRef(null);
  const { isTamil } = useTheme();

  useEffect(() => {
    // Add welcome message
    setMessages([
      {
        id: 1,
        role: 'assistant',
        content: isTamil
          ? 'வணக்கம்! நான் திருவள்ளுவர். உங்கள் வாழ்க்கையில் ஏதேனும் கேள்விகள் அல்லது சந்தேகங்கள் இருந்தால், தயங்காமல் கேளுங்கள். திருக்குறளின் ஞானத்தின் மூலம் உங்களுக்கு வழிகாட்ட நான் இங்கே இருக்கிறேன்.'
          : 'Greetings! I am <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. If you have any questions or doubts in your life, feel free to ask. I am here to guide you through the wisdom of Thirukkural.',
        timestamp: new Date(),
        suggestedKural: null
      }
    ]);

    // Fetch wisdom topics
    fetchWisdomTopics();
  }, [isTamil]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchWisdomTopics = async () => {
    try {
      const response = await chatAPI.getWisdomTopics();
      setWisdomTopics(response.data.data);
    } catch (err) {
      console.error('Failed to fetch wisdom topics:', err);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const sendMessage = async (messageText = inputMessage) => {
    if (!messageText.trim()) return;

    const userMessage = {
      id: Date.now(),
      role: 'user',
      content: messageText.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setLoading(true);
    setError(null);

    try {
      // Get conversation history (last 6 messages)
      const conversationHistory = messages.slice(-6).map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      const response = await chatAPI.chatWithAI(messageText.trim(), conversationHistory);
      const { response: aiResponse, suggestedKural, relevantKurals } = response.data.data;

      const assistantMessage = {
        id: Date.now() + 1,
        role: 'assistant',
        content: aiResponse,
        timestamp: new Date(),
        suggestedKural,
        relevantKurals
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to get response');

      const errorMessage = {
        id: Date.now() + 1,
        role: 'assistant',
        content: isTamil
          ? 'மன்னிக்கவும், தற்போது என்னால் பதிலளிக்க முடியவில்லை. தயவுசெய்து பின்னர் முயற்சிக்கவும்.'
          : 'I apologize, I cannot respond right now. Please try again later.',
        timestamp: new Date(),
        isError: true
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    sendMessage();
  };

  const handleTopicClick = (topic) => {
    sendMessage(topic);
  };

  const clearChat = () => {
    setMessages([
      {
        id: 1,
        role: 'assistant',
        content: isTamil
          ? 'வணக்கம்! நான் திருவள்ளுவர். உங்கள் வாழ்க்கையில் ஏதேனும் கேள்விகள் அல்லது சந்தேகங்கள் இருந்தால், தயங்காமல் கேளுங்கள்.'
          : 'Greetings! I am Thiruvalluvar. If you have any questions or doubts in your life, feel free to ask.',
        timestamp: new Date(),
        suggestedKural: null
      }
    ]);
    setError(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-full flex items-center justify-center mr-4">
              <Bot className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold gradient-text">
                {isTamil ? 'திருவள்ளுவருடன் உரையாடல்' : 'Chat with Thiruvalluvar'}
              </h1>
              <p className="text-muted-foreground">
                {isTamil
                  ? 'திருக்குறளின் ஞானத்துடன் வாழ்க்கை வழிகாட்டுதல் பெறுங்கள்'
                  : 'Get life guidance with the wisdom of Thirukkural'
                }
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Chat Interface */}
          <div className="lg:col-span-3">
            <div className="card h-[600px] flex flex-col">
              {/* Chat Header */}
              <div className="card-header border-b">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <Sparkles className="w-5 h-5 text-primary-600 mr-2" />
                    <h3 className="card-title">
                      {isTamil ? 'ஞான உரையாடல்' : 'Wisdom Chat'}
                    </h3>
                  </div>
                  <button
                    onClick={clearChat}
                    className="btn-ghost btn-sm"
                    title={isTamil ? 'உரையாடலை அழி' : 'Clear chat'}
                  >
                    <RefreshCw className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.map((message) => (
                  <ChatMessage
                    key={message.id}
                    message={message}
                    isTamil={isTamil}
                  />
                ))}

                {loading && (
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                      <Bot className="w-4 h-4 text-white" />
                    </div>
                    <div className="bg-gray-100 rounded-lg p-3 max-w-xs">
                      <LoadingSpinner size="sm" text={isTamil ? 'சிந்திக்கிறேன்...' : 'Thinking...'} />
                    </div>
                  </div>
                )}

                <div ref={messagesEndRef} />
              </div>

              {/* Input */}
              <div className="border-t p-4">
                <form onSubmit={handleSubmit} className="flex space-x-2">
                  <input
                    type="text"
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    placeholder={isTamil
                      ? 'உங்கள் கேள்வியை இங்கே தட்டச்சு செய்யுங்கள்...'
                      : 'Type your question here...'
                    }
                    className="input flex-1"
                    disabled={loading}
                  />
                  <button
                    type="submit"
                    disabled={loading || !inputMessage.trim()}
                    className="btn-primary btn-md disabled:opacity-50"
                  >
                    <Send className="w-4 h-4" />
                  </button>
                </form>

                {error && (
                  <div className="mt-2 text-sm text-red-600">
                    {error}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Topics */}
            <div className="card">
              <div className="card-header">
                <h3 className="card-title flex items-center">
                  <Lightbulb className="w-5 h-5 mr-2 text-primary-600" />
                  {isTamil ? 'ஞான தலைப்புகள்' : 'Wisdom Topics'}
                </h3>
              </div>
              <div className="card-content">
                <div className="space-y-2">
                  {wisdomTopics.slice(0, 2).map((category, index) => (
                    <div key={index}>
                      <h4 className="font-medium text-sm mb-2">{category.category}</h4>
                      <div className="space-y-1">
                        {category.topics.slice(0, 3).map((topic, topicIndex) => (
                          <button
                            key={topicIndex}
                            onClick={() => handleTopicClick(topic)}
                            className="block w-full text-left text-xs p-2 rounded hover:bg-accent transition-colors"
                          >
                            {topic}
                          </button>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Sample Questions */}
            <div className="card">
              <div className="card-header">
                <h3 className="card-title flex items-center">
                  <MessageCircle className="w-5 h-5 mr-2 text-primary-600" />
                  {isTamil ? 'மாதிரி கேள்விகள்' : 'Sample Questions'}
                </h3>
              </div>
              <div className="card-content">
                <div className="space-y-2">
                  {[
                    isTamil ? 'நல்ல வாழ்க்கை எப்படி வாழ்வது?' : 'How to live a good life?',
                    isTamil ? 'கோபத்தை எப்படி கட்டுப்படுத்துவது?' : 'How to control anger?',
                    isTamil ? 'உண்மையான நட்பு என்றால் என்ன?' : 'What is true friendship?',
                    isTamil ? 'செல்வத்தை எப்படி சரியாக பயன்படுத்துவது?' : 'How to use wealth properly?'
                  ].map((question, index) => (
                    <button
                      key={index}
                      onClick={() => handleTopicClick(question)}
                      className="block w-full text-left text-xs p-2 rounded hover:bg-accent transition-colors"
                    >
                      {question}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* About */}
            <div className="card">
              <div className="card-content text-center">
                <Bot className="w-12 h-12 text-primary-600 mx-auto mb-3" />
                <h4 className="font-semibold mb-2">
                  {isTamil ? 'AI திருவள்ளுவர்' : 'AI Thiruvalluvar'}
                </h4>
                <p className="text-xs text-muted-foreground mb-3">
                  {isTamil
                    ? 'திருக்குறளின் ஞானத்துடன் இயங்கும் AI உதவியாளர்'
                    : 'AI assistant powered by Thirukkural wisdom'
                  }
                </p>
                <Link to="/about" className="btn-outline btn-sm w-full">
                  {isTamil ? 'மேலும் அறிய' : 'Learn More'}
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Chat Message Component
const ChatMessage = ({ message, isTamil }) => {
  const isUser = message.role === 'user';

  return (
    <div className={`flex items-start space-x-3 ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`}>
      {/* Avatar */}
      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
        isUser ? 'bg-secondary-600' : 'bg-primary-600'
      }`}>
        {isUser ? (
          <User className="w-4 h-4 text-white" />
        ) : (
          <Bot className="w-4 h-4 text-white" />
        )}
      </div>

      {/* Message Content */}
      <div className={`max-w-xs lg:max-w-md ${isUser ? 'text-right' : ''}`}>
        <div className={`rounded-lg p-3 ${
          isUser
            ? 'bg-secondary-600 text-white'
            : message.isError
              ? 'bg-red-100 text-red-800'
              : 'bg-gray-100 text-gray-800'
        }`}>
          <p className="text-sm whitespace-pre-wrap">{message.content}</p>
        </div>

        {/* Suggested Kural */}
        {message.suggestedKural && (
          <div className="mt-3 p-3 bg-primary-50 rounded-lg border border-primary-200">
            <div className="flex items-center mb-2">
              <BookOpen className="w-4 h-4 text-primary-600 mr-2" />
              <span className="text-xs font-medium text-primary-600">
                {isTamil ? 'பரிந்துரைக்கப்பட்ட குறள்' : 'Suggested Kural'}
              </span>
            </div>
            <Link
              to={`/kural/${message.suggestedKural.number}`}
              className="block hover:bg-primary-100 rounded p-2 transition-colors"
            >
              <div className="text-xs text-muted-foreground mb-1">
                {isTamil ? 'குறள்' : 'Kural'} {message.suggestedKural.number}
              </div>
              <div className="kural-text text-sm text-primary-800 mb-2">
                {message.suggestedKural.line1}<br />
                {message.suggestedKural.line2}
              </div>
              <p className="text-xs text-muted-foreground">
                {message.suggestedKural.meaning_en}
              </p>
            </Link>
          </div>
        )}

        {/* Timestamp */}
        <div className={`text-xs text-muted-foreground mt-1 ${isUser ? 'text-right' : ''}`}>
          {message.timestamp.toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
};

export default Chat;
