import { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { useTheme } from '../hooks/useTheme';
import { chatAPI, kuralAPI } from '../services/api';
import {
  Send,
  Sparkles,
  BookOpen,
  MessageCircle,
  User,
  Bot,
  Lightbulb,
  RefreshCw
} from 'lucide-react';

const Chat = () => {
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [wisdomTopics, setWisdomTopics] = useState([]);
  const messagesEndRef = useRef(null);
  const { isTamil } = useTheme();

  useEffect(() => {
    // Add welcome message
    setMessages([
      {
        id: 1,
        role: 'assistant',
        content: isTamil
          ? 'வணக்கம்! நான் திருவள்ளுவர். உங்கள் வாழ்க்கையில் ஏதேனும் கேள்விகள் அல்லது சந்தேகங்கள் இருந்தால், தயங்காமல் கேளுங்கள். திருக்குறளின் ஞானத்தின் மூலம் உங்களுக்கு வழிகாட்ட நான் இங்கே இருக்கிறேன்.'
          : 'Greetings! I am <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. If you have any questions or doubts in your life, feel free to ask. I am here to guide you through the wisdom of Thirukkural.',
        timestamp: new Date(),
      }
    ]);

    // Fetch wisdom topics
    fetchWisdomTopics();
  }, [isTamil]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchWisdomTopics = async () => {
    try {
      const response = await chatAPI.getWisdomTopics();
      setWisdomTopics(response.data.data);
    } catch (err) {
      console.error('Failed to fetch wisdom topics:', err);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Function to detect and parse kural references from AI response
  const parseKuralReferences = async (text) => {
    // Regex to find "குறள்-[number]" pattern (Tamil format)
    const kuralPattern = /குறள்-(\d+)/gi;
    const matches = text.match(kuralPattern);

    if (!matches || matches.length === 0) {
      return [];
    }

    // Extract unique kural numbers
    const kuralNumbers = [...new Set(matches.map(match => {
      const number = parseInt(match.replace(/குறள்-/i, ''));
      return number >= 1 && number <= 1330 ? number : null;
    }).filter(Boolean))];

    // Fetch kural data for each number
    const detectedKurals = [];
    for (const number of kuralNumbers) {
      try {
        const response = await kuralAPI.getKural(number);
        if (response.data.success) {
          detectedKurals.push({
            number: response.data.data.number,
            line1: response.data.data.line1,
            line2: response.data.data.line2,
            meaning_ta: response.data.data.meaning_ta,
            meaning_en: response.data.data.meaning_en,
            adhigaram: response.data.data.adhigaram,
            paal: response.data.data.paal
          });
        }
      } catch (error) {
        console.error(`Failed to fetch kural ${number}:`, error);
      }
    }

    return detectedKurals;
  };

  const sendMessage = async (messageText = inputMessage) => {
    if (!messageText.trim()) return;

    const userMessage = {
      id: Date.now(),
      role: 'user',
      content: messageText.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setLoading(true);
    setError(null);

    try {
      // Get conversation history (last 6 messages)
      const conversationHistory = messages.slice(-6).map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      const language = isTamil ? 'tamil' : 'english';
      const response = await chatAPI.chatWithAI(messageText.trim(), conversationHistory, language);

      // Handle both AI and fallback responses
      const responseData = response.data.data || response.data; // Handle nested structure

      const aiResponse = responseData.response;
      const isAI = responseData.isAI !== false; // Default to true if not specified
      const fallbackMessage = responseData.message;
      const relevantKurals = responseData.relevantKurals;

      // Parse kural references from AI response
      const detectedKurals = await parseKuralReferences(aiResponse);

      const assistantMessage = {
        id: Date.now() + 1,
        role: 'assistant',
        content: aiResponse,
        timestamp: new Date(),
        relevantKurals,
        detectedKurals, // Add detected kurals from AI response
        isAI,
        fallbackMessage
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to get response');

      const errorMessage = {
        id: Date.now() + 1,
        role: 'assistant',
        content: isTamil
          ? 'மன்னிக்கவும், தற்போது என்னால் பதிலளிக்க முடியவில்லை. தயவுசெய்து பின்னர் முயற்சிக்கவும்.'
          : 'I apologize, I cannot respond right now. Please try again later.',
        timestamp: new Date(),
        isError: true
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    sendMessage();
  };

  const handleTopicClick = (topic) => {
    sendMessage(topic);
  };

  const clearChat = () => {
    setMessages([
      {
        id: 1,
        role: 'assistant',
        content: isTamil
          ? 'வணக்கம்! நான் திருவள்ளுவர். உங்கள் வாழ்க்கையில் ஏதேனும் கேள்விகள் அல்லது சந்தேகங்கள் இருந்தால், தயங்காமல் கேளுங்கள்.'
          : 'Greetings! I am Thiruvalluvar. If you have any questions or doubts in your life, feel free to ask.',
        timestamp: new Date(),
      }
    ]);
    setError(null);
  };

  return (
    <div className="min-h-screen gradient-bg-heritage relative overflow-hidden">
      {/* Heritage Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-r from-heritage-100/15 via-temple-100/10 to-heritage-200/15"></div>

      {/* Traditional Decorative Elements */}
      <div className="absolute top-20 left-20 w-20 h-20 border-2 border-heritage-300/30 rounded-full opacity-40" style={{animation: 'gentle-float 8s ease-in-out infinite'}}></div>
      <div className="absolute bottom-20 right-20 w-16 h-16 border-2 border-temple-300/30 rounded-full opacity-35" style={{animation: 'gentle-float 10s ease-in-out infinite', animationDelay: '2s'}}></div>
      <div className="absolute top-1/2 left-10 w-12 h-12 bg-heritage-200/20 rounded-full opacity-30" style={{animation: 'gentle-float 12s ease-in-out infinite', animationDelay: '1s'}}></div>
      <div className="absolute top-1/4 right-1/4 w-8 h-8 bg-temple-200/25 rounded-full opacity-25" style={{animation: 'gentle-float 14s ease-in-out infinite', animationDelay: '3s'}}></div>

      <div className="container mx-auto px-4 py-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-full flex items-center justify-center mr-4">
              <Bot className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold gradient-text-heritage drop-shadow-md">
                {isTamil ? 'திருவள்ளுவருடன் உரையாடல்' : 'Chat with Thiruvalluvar'}
              </h1>
              <p className="text-muted-foreground">
                {isTamil
                  ? 'திருக்குறளின் ஞானத்துடன் வாழ்க்கை வழிகாட்டுதல் பெறுங்கள்'
                  : 'Get life guidance with the wisdom of Thirukkural'
                }
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Chat Interface */}
          <div className="lg:col-span-3">
            <div className="card-heritage h-[600px] flex flex-col transition-all duration-300 animate-slide-up">
              {/* Chat Header */}
              <div className="card-header border-b bg-gradient-to-r from-heritage-50 via-temple-50 to-heritage-100 border-heritage-200/30">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gradient-to-br from-heritage-600 to-heritage-700 rounded-full flex items-center justify-center mr-3 border border-heritage-300/30">
                      <Sparkles className="w-4 h-4 text-heritage-50" />
                    </div>
                    <div>
                      <h3 className="card-title text-heritage-800">
                        {isTamil ? 'ஞான உரையாடல்' : 'Wisdom Chat'}
                      </h3>
                      <div className="text-xs text-temple-600">
                        {isTamil ? 'திருவள்ளுவருடன்' : 'with Thiruvalluvar'}
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={clearChat}
                    className="btn-ghost btn-sm hover:bg-primary-100 transition-colors duration-200"
                    title={isTamil ? 'உரையாடலை அழி' : 'Clear chat'}
                  >
                    <RefreshCw className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.map((message) => (
                  <ChatMessage
                    key={message.id}
                    message={message}
                    isTamil={isTamil}
                  />
                ))}

                {loading && (
                  <div className="flex items-start space-x-3 animate-fade-in">
                    <div className="w-8 h-8 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-full flex items-center justify-center animate-pulse">
                      <Bot className="w-4 h-4 text-white" />
                    </div>
                    <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-3 max-w-xs border border-gray-200 shadow-sm">
                      <div className="flex items-center space-x-2">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                          <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {isTamil ? 'சிந்திக்கிறேன்...' : 'Thinking...'}
                        </span>
                      </div>
                    </div>
                  </div>
                )}

                <div ref={messagesEndRef} />
              </div>

              {/* Input */}
              <div className="border-t bg-gradient-to-r from-gray-50 to-gray-100 p-4">
                <form onSubmit={handleSubmit} className="flex space-x-3">
                  <div className="flex-1 relative">
                    <input
                      type="text"
                      value={inputMessage}
                      onChange={(e) => setInputMessage(e.target.value)}
                      placeholder={isTamil
                        ? 'உங்கள் கேள்வியை இங்கே தட்டச்சு செய்யுங்கள்...'
                        : 'Type your question here...'
                      }
                      className="input w-full pr-12 border-2 border-gray-200 focus:border-primary-400 transition-colors duration-200"
                      disabled={loading}
                    />
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <MessageCircle className="w-4 h-4 text-gray-400" />
                    </div>
                  </div>
                  <button
                    type="submit"
                    disabled={loading || !inputMessage.trim()}
                    className="btn-primary btn-md disabled:opacity-50 hover:scale-105 active:scale-95 transition-transform duration-200 shadow-md hover:shadow-lg"
                  >
                    <Send className="w-4 h-4" />
                  </button>
                </form>

                {error && (
                  <div className="mt-2 text-sm text-red-600">
                    {error}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Topics */}
            <div className="card-heritage transition-all duration-300 animate-slide-up">
              <div className="card-header bg-gradient-to-r from-heritage-50 via-heritage-100 to-heritage-50 border-b border-heritage-200/30">
                <h3 className="card-title flex items-center">
                  <div className="w-6 h-6 bg-gradient-to-br from-amber-500 to-yellow-600 rounded-full flex items-center justify-center mr-2">
                    <Lightbulb className="w-3 h-3 text-white" />
                  </div>
                  {isTamil ? 'ஞான தலைப்புகள்' : 'Wisdom Topics'}
                </h3>
              </div>
              <div className="card-content">
                <div className="space-y-3">
                  {wisdomTopics.slice(0, 2).map((category, index) => (
                    <div key={index} className="animate-fade-in" style={{animationDelay: `${index * 0.1}s`}}>
                      <h4 className="font-medium text-sm mb-2 text-primary-700">{category.category}</h4>
                      <div className="space-y-1">
                        {category.topics.slice(0, 3).map((topic, topicIndex) => (
                          <button
                            key={topicIndex}
                            onClick={() => handleTopicClick(topic)}
                            className="block w-full text-left text-xs p-2 rounded-md hover:bg-gradient-to-r hover:from-primary-50 hover:to-secondary-50 transition-all duration-200 border border-transparent hover:border-primary-200 hover:shadow-sm"
                          >
                            {topic}
                          </button>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Sample Questions */}
            <div className="card-heritage transition-all duration-300 animate-slide-up">
              <div className="card-header bg-gradient-to-r from-temple-50 via-temple-100 to-temple-50 border-b border-temple-200/30">
                <h3 className="card-title flex items-center">
                  <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mr-2">
                    <MessageCircle className="w-3 h-3 text-white" />
                  </div>
                  {isTamil ? 'மாதிரி கேள்விகள்' : 'Sample Questions'}
                </h3>
              </div>
              <div className="card-content">
                <div className="space-y-2">
                  {[
                    isTamil ? 'நல்ல வாழ்க்கை எப்படி வாழ்வது?' : 'How to live a good life?',
                    isTamil ? 'கோபத்தை எப்படி கட்டுப்படுத்துவது?' : 'How to control anger?',
                    isTamil ? 'உண்மையான நட்பு என்றால் என்ன?' : 'What is true friendship?',
                    isTamil ? 'செல்வத்தை எப்படி சரியாக பயன்படுத்துவது?' : 'How to use wealth properly?'
                  ].map((question, index) => (
                    <button
                      key={index}
                      onClick={() => handleTopicClick(question)}
                      className="block w-full text-left text-xs p-2 rounded-md hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-200 border border-transparent hover:border-blue-200 hover:shadow-sm animate-fade-in"
                      style={{animationDelay: `${index * 0.1}s`}}
                    >
                      {question}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* About */}
            <div className="card">
              <div className="card-content text-center">
                <Bot className="w-12 h-12 text-primary-600 mx-auto mb-3" />
                <h4 className="font-semibold mb-2">
                  {isTamil ? 'AI திருவள்ளுவர்' : 'AI Thiruvalluvar'}
                </h4>
                <p className="text-xs text-muted-foreground mb-3">
                  {isTamil
                    ? 'திருக்குறளின் ஞானத்துடன் இயங்கும் AI உதவியாளர்'
                    : 'AI assistant powered by Thirukkural wisdom'
                  }
                </p>
                <Link to="/about" className="btn-outline btn-sm w-full">
                  {isTamil ? 'மேலும் அறிய' : 'Learn More'}
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Chat Message Component
const ChatMessage = ({ message, isTamil }) => {
  const isUser = message.role === 'user';

  return (
    <div className={`flex items-start space-x-3 ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`}>
      {/* Avatar */}
      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
        isUser ? 'bg-secondary-600' : 'bg-primary-600'
      }`}>
        {isUser ? (
          <User className="w-4 h-4 text-white" />
        ) : (
          <Bot className="w-4 h-4 text-white" />
        )}
      </div>

      {/* Message Content */}
      <div className={`max-w-xs lg:max-w-md ${isUser ? 'text-right' : ''}`}>
        <div className={`rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md ${
          isUser
            ? 'bg-gradient-to-r from-heritage-600 to-heritage-700 text-heritage-50 border border-heritage-500/30'
            : message.isError
              ? 'bg-red-50 text-red-800 border border-red-200'
              : 'bg-gradient-to-r from-heritage-50 to-temple-50 text-temple-800 border border-heritage-200/50'
        }`}>
          <p className="text-sm whitespace-pre-wrap leading-relaxed">{message.content}</p>

          {/* Fallback message indicator */}
          {!isUser && message.isAI === false && message.fallbackMessage && (
            <div className="mt-3 pt-3 border-t border-heritage-200/50">
              <div className="flex items-center text-xs text-heritage-700 bg-heritage-50/50 rounded-md p-2">
                <Sparkles className="w-3 h-3 mr-2 text-heritage-600" />
                {message.fallbackMessage}
              </div>
            </div>
          )}
        </div>

        {/* Detected Kurals from AI Response */}
        {message.detectedKurals && message.detectedKurals.length > 0 && (
          <div className="mt-4 space-y-3">
            {message.detectedKurals.map((kural) => (
              <div key={kural.number} className="p-4 bg-gradient-to-r from-heritage-50/90 via-temple-50/60 to-heritage-100/80 rounded-lg border border-heritage-200/50 shadow-sm">
                <div className="flex items-center mb-3">
                  <Sparkles className="w-4 h-4 text-heritage-600 mr-2" />
                  <span className="text-sm font-medium text-heritage-700">
                    {isTamil ? 'AI பரிந்துரைத்த குறள்' : 'AI Suggested Kural'}
                  </span>
                </div>
                <Link
                  to={`/kural/${kural.number}`}
                  className="block hover:bg-heritage-100/60 rounded-lg p-3 transition-all duration-200 border border-transparent hover:border-heritage-200/60 hover:shadow-md group cursor-pointer"
                  onClick={() => {
                    console.log(`Navigating to AI suggested Kural ${kural.number}`);
                  }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="text-sm font-medium text-heritage-800">
                      {isTamil ? 'குறள்' : 'Kural'} {kural.number}
                    </div>
                    <div className="text-xs text-heritage-600 bg-heritage-100/50 px-2 py-1 rounded">
                      {kural.adhigaram}
                    </div>
                  </div>
                  <div className="kural-text text-base text-heritage-800 mb-3 leading-relaxed font-medium">
                    {kural.line1}<br />
                    {kural.line2}
                  </div>
                  <p className="text-sm text-temple-600 leading-relaxed">
                    {isTamil ? kural.meaning_ta || kural.meaning_en : kural.meaning_en}
                  </p>
                  <div className="mt-2 text-xs text-heritage-600 group-hover:text-heritage-700 transition-colors flex items-center">
                    <span>{isTamil ? 'குறளைப் பார்க்க கிளிக் செய்யுங்கள்' : 'Click to view kural'}</span>
                    <span className="ml-1 group-hover:translate-x-1 transition-transform duration-200">→</span>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        )}

        

        {/* Timestamp */}
        <div className={`text-xs text-muted-foreground mt-1 ${isUser ? 'text-right' : ''}`}>
          {message.timestamp.toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
};

export default Chat;
