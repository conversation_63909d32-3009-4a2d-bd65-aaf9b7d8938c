import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useTheme } from '../hooks/useTheme';
import { kuralAPI } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';
import { 
  Search, 
  BookOpen, 
  MessageCircle, 
  Heart, 
  TrendingUp,
  ArrowRight,
  Sparkles
} from 'lucide-react';

const Home = () => {
  const [kuralOfTheDay, setKuralOfTheDay] = useState(null);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const { isTamil } = useTheme();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchHomeData = async () => {
      try {
        const [kuralResponse, statsResponse] = await Promise.all([
          kuralAPI.getRandomKural(),
          kuralAPI.getStats()
        ]);

        setKuralOfTheDay(kuralResponse.data.data);
        setStats(statsResponse.data.data);
      } catch (error) {
        console.error('Error fetching home data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchHomeData();
  }, []);

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const categories = [
    {
      name: 'அறம்',
      nameEn: 'Virtue',
      description: isTamil 
        ? 'நல்லொழுக்கம், அறநெறி, ஒழுக்கம்' 
        : 'Ethics, righteousness, and moral conduct',
      color: 'from-emerald-400 via-green-500 to-teal-600',
      bgColor: 'from-emerald-50 to-green-100',
      icon: '🕊️'
    },
    {
      name: 'பொருள்',
      nameEn: 'Wealth',
      description: isTamil 
        ? 'செல்வம், அரசியல், பொருளாதாரம்' 
        : 'Prosperity, governance, and economics',
      color: 'from-amber-400 via-orange-500 to-red-600',
      bgColor: 'from-amber-50 to-orange-100',
      icon: '👑'
    },
    {
      name: 'இன்பம்',
      nameEn: 'Love',
      description: isTamil 
        ? 'காதல், இன்பம், உறவுகள்' 
        : 'Love, pleasure, and relationships',
      color: 'from-pink-400 via-rose-500 to-purple-600',
      bgColor: 'from-pink-50 to-rose-100',
      icon: '💝'
    }
  ];

  const features = [
    {
      icon: BookOpen,
      title: isTamil ? 'முழுமையான குறள் தொகுப்பு' : 'Complete Kural Collection',
      description: isTamil 
        ? '1330 குறள்கள் தமிழ் மற்றும் ஆங்கில மொழிபெயர்ப்புடன்'
        : 'All 1330 Kurals with Tamil and English translations'
    },
    {
      icon: MessageCircle,
      title: isTamil ? 'AI வள்ளுவர் உரையாடல்' : 'AI Thiruvalluvar Chat',
      description: isTamil 
        ? 'திருவள்ளுவருடன் நேரடியாக உரையாடி ஞானம் பெறுங்கள்'
        : 'Chat directly with Thiruvalluvar AI for wisdom and guidance'
    },
    {
      icon: Heart,
      title: isTamil ? 'தனிப்பட்ட சேகரிப்பு' : 'Personal Collection',
      description: isTamil 
        ? 'விருப்பமான குறள்களை சேமித்து வைத்துக்கொள்ளுங்கள்'
        : 'Save your favorite Kurals and track your reading progress'
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" text={isTamil ? 'ஏற்றுகிறது...' : 'Loading...'} />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="gradient-bg-vibrant py-20 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-r from-primary-200/30 to-secondary-200/30 animate-pulse-slow"></div>
        <div className="absolute top-10 left-10 w-20 h-20 bg-gradient-to-br from-accent-400 to-accent-600 rounded-full opacity-30 animate-bounce shadow-lg"></div>
        <div className="absolute bottom-10 right-10 w-16 h-16 bg-gradient-to-br from-success-400 to-success-600 rounded-full opacity-30 animate-bounce shadow-lg" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 left-1/4 w-12 h-12 bg-gradient-to-br from-warning-400 to-warning-600 rounded-full opacity-20 animate-pulse" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/3 right-1/4 w-8 h-8 bg-gradient-to-br from-error-400 to-error-600 rounded-full opacity-25 animate-bounce" style={{animationDelay: '3s'}}></div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 animate-fade-in">
              <span className="gradient-text-vibrant drop-shadow-lg">
                {isTamil ? 'குறள்வெர்ஸ்' : 'KuralVerse'}
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 animate-slide-up">
              {isTamil
                ? 'திருக்குறளின் அழியாத ஞானத்தை நவீன உலகில் கொண்டு வருகிறோம்'
                : 'Bringing the timeless wisdom of Thirukkural to the modern world'
              }
            </p>
            
            {/* Search Bar */}
            <form onSubmit={handleSearch} className="max-w-2xl mx-auto mb-8">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder={isTamil 
                    ? 'குறள் எண் அல்லது உள்ளடக்கம் தேடுங்கள்...'
                    : 'Search by Kural number or content...'
                  }
                  className="w-full pl-12 pr-4 py-4 text-lg rounded-xl border-2 border-transparent bg-white/90 backdrop-blur focus:border-primary-500 focus:outline-none shadow-lg"
                />
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 btn-primary btn-md"
                >
                  {isTamil ? 'தேடு' : 'Search'}
                </button>
              </div>
            </form>

            {/* Quick Actions */}
            <div className="flex flex-wrap justify-center gap-4 animate-slide-up">
              <Link to="/kurals" className="btn-primary btn-lg group">
                <BookOpen className="w-5 h-5 mr-2 group-hover:animate-bounce" />
                {isTamil ? 'குறள்களை ஆராயுங்கள்' : 'Explore Kurals'}
              </Link>
              <Link to="/chat" className="btn-outline btn-lg group">
                <MessageCircle className="w-5 h-5 mr-2 group-hover:animate-pulse" />
                {isTamil ? 'வள்ளுவருடன் பேசுங்கள்' : 'Chat with Valluvar'}
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Kural of the Day */}
      {kuralOfTheDay && (
        <section className="py-16 bg-gradient-to-r from-accent-50/80 via-primary-50/60 to-secondary-50/80 relative overflow-hidden">
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-br from-accent-100/20 via-primary-100/15 to-secondary-100/20"></div>
          <div className="absolute top-10 right-10 w-32 h-32 bg-gradient-to-br from-accent-300/20 to-accent-500/20 rounded-full animate-pulse-slow"></div>
          <div className="absolute bottom-10 left-10 w-24 h-24 bg-gradient-to-br from-primary-300/20 to-primary-500/20 rounded-full animate-pulse-slow" style={{animationDelay: '1s'}}></div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-4xl mx-auto text-center">
              <div className="flex items-center justify-center mb-6 animate-fade-in">
                <Sparkles className="w-6 h-6 text-primary-600 mr-2 animate-pulse" />
                <h2 className="text-2xl md:text-3xl font-bold gradient-text">
                  {isTamil ? 'இன்றைய குறள்' : 'Kural of the Day'}
                </h2>
              </div>

              <div className="card-vibrant max-w-2xl mx-auto card-glow transition-all duration-500 animate-scale-in">
                <div className="card-content text-center p-8 bg-gradient-to-br from-white/90 via-accent-50/50 to-primary-50/60">
                  <div className="text-sm text-muted-foreground mb-2">
                    {isTamil ? 'குறள்' : 'Kural'} {kuralOfTheDay.number} - {kuralOfTheDay.adhigaram}
                  </div>
                  <div className="kural-text text-2xl mb-4 text-primary-800">
                    {kuralOfTheDay.line1}<br />
                    {kuralOfTheDay.line2}
                  </div>
                  <p className="kural-meaning text-lg mb-4">
                    {isTamil ? kuralOfTheDay.meaning_ta : kuralOfTheDay.meaning_en}
                  </p>
                  <Link 
                    to={`/kural/${kuralOfTheDay.number}`}
                    className="btn-primary btn-md"
                  >
                    {isTamil ? 'மேலும் படிக்க' : 'Read More'}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Categories */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">
            {isTamil ? 'பால்கள்' : 'Categories'}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {categories.map((category) => (
              <Link
                key={category.name}
                to={`/category/${encodeURIComponent(category.name)}`}
                className="group"
              >
                <div className={`card-vibrant hover:shadow-xl transition-all duration-500 group-hover:scale-110 group-hover:-rotate-2 bg-gradient-to-br ${category.bgColor || 'from-white to-gray-50'}`}>
                  <div className="card-content text-center p-6">
                    <div className={`w-20 h-20 mx-auto mb-6 rounded-2xl bg-gradient-to-r ${category.color} flex items-center justify-center text-3xl shadow-xl group-hover:shadow-2xl transition-all duration-500 group-hover:scale-110 group-hover:rotate-12`}>
                      {category.icon}
                    </div>
                    <h3 className="text-2xl font-bold mb-3 bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                      {isTamil ? category.name : category.nameEn}
                    </h3>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {category.description}
                    </p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">
            {isTamil ? 'சிறப்பு அம்சங்கள்' : 'Features'}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-primary-100 rounded-full flex items-center justify-center">
                  <feature.icon className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                <p className="text-muted-foreground">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats */}
      {stats && (
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-3xl font-bold text-primary-600 mb-2">
                  {stats.totalKurals}
                </div>
                <div className="text-muted-foreground">
                  {isTamil ? 'குறள்கள்' : 'Kurals'}
                </div>
              </div>
              <div>
                <div className="text-3xl font-bold text-primary-600 mb-2">
                  133
                </div>
                <div className="text-muted-foreground">
                  {isTamil ? 'அதிகாரங்கள்' : 'Chapters'}
                </div>
              </div>
              <div>
                <div className="text-3xl font-bold text-primary-600 mb-2">
                  3
                </div>
                <div className="text-muted-foreground">
                  {isTamil ? 'பால்கள்' : 'Sections'}
                </div>
              </div>
              <div>
                <div className="text-3xl font-bold text-primary-600 mb-2">
                  2000+
                </div>
                <div className="text-muted-foreground">
                  {isTamil ? 'ஆண்டுகள்' : 'Years Old'}
                </div>
              </div>
            </div>
          </div>
        </section>
      )}
    </div>
  );
};

export default Home;
